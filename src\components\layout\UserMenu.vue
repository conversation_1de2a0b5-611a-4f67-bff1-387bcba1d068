<template>
  <div class="user-menu" ref="userMenuRef" :class="{ 'mobile-context': isMobileContext, 'store-mode': isStoreMode }">
    <!-- Mobile User Section (quando dentro do menu mobile) -->
    <div v-if="isMobileContext" class="mobile-user-section">
      <div class="mobile-user-header">
        <div class="mobile-user-info">
          <div class="mobile-user-avatar">
            <img
              v-if="displayInfo.avatarUrl"
              :src="displayInfo.avatarUrl"
              :alt="displayInfo.name"
              class="avatar-image"
              @error="handleMobileAvatarError"
            />
            <img
              v-else
              :src="generateMobileUserAvatar()"
              :alt="displayInfo.name"
              class="avatar-image"
            />
          </div>
          <div class="mobile-user-details">
            <h3 class="mobile-user-name">{{ displayInfo.name }}</h3>
            <p class="mobile-user-email">{{ displayInfo.email }}</p>
          </div>
        </div>
      </div>

      <!-- Mobile Menu Items -->
      <div class="mobile-menu-items">
        <!-- User Mode Items -->
        <template v-if="!isStoreMode">
          <button @click="navigateToProfile" class="mobile-menu-item">
            <HugeiconsIcon :icon="UserAccountIcon" size="20" :strokeWidth="1.5" />
            <span>{{ t('userMenu.myProfile') }}</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
          <button @click="navigateToPreferences" class="mobile-menu-item">
            <HugeiconsIcon :icon="Settings02Icon" size="20" :strokeWidth="1.5" />
            <span>{{ t('userMenu.preferences') }}</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
          <button @click="navigateToAccount" class="mobile-menu-item">
            <HugeiconsIcon :icon="UserAccountIcon" size="20" :strokeWidth="1.5" />
            <span>{{ t('userMenu.accountSettings') }}</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
          <button @click="navigateToAllStores" class="mobile-menu-item">
            <HugeiconsIcon :icon="Store01Icon" size="20" :strokeWidth="1.5" />
            <span>{{ t('userMenu.allStores') }}</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
          <button @click="navigateToChangePassword" class="mobile-menu-item">
            <HugeiconsIcon :icon="SquareUnlock02Icon" size="20" :strokeWidth="1.5" />
            <span>{{ t('userMenu.changePassword') }}</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
        </template>
        
        <!-- Store Mode Items -->
        <template v-if="isStoreMode">
          <button v-if="canViewTeam" @click="navigateToTeamManagement" class="mobile-menu-item">
            <HugeiconsIcon :icon="UserGroup02Icon" size="20" :strokeWidth="1.5" />
            <span>Gestão de Equipe</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
          <button @click="navigateToAllStores" class="mobile-menu-item">
            <HugeiconsIcon :icon="Store01Icon" size="20" :strokeWidth="1.5" />
            <span>{{ t('userMenu.allStores') }}</span>
            <ChevronRight class="mobile-item-arrow" size="16" :strokeWidth="1.5" />
          </button>
        </template>
        
        <button @click="handleLogout" class="mobile-logout-button">
          <HugeiconsIcon :icon="LogoutIcon" size="20" :strokeWidth="1.5" />
          <span>{{ t('userMenu.logout') }}</span>
        </button>
      </div>
    </div>

    <!-- Desktop User Toggle Button -->
    <button
      v-else
      ref="toggleButtonRef"
      @click="toggleDropdown"
      class="user-toggle-button"
      :class="{ 'active': isOpen }"
      :title="t('userMenu.currentUser', { name: displayInfo.name })"
    >
      <div class="user-avatar">
        <img
          v-if="displayInfo.avatarUrl"
          :src="displayInfo.avatarUrl"
          :alt="displayInfo.name"
          class="avatar-image"
          @error="handleDesktopAvatarError"
        />
        <img
          v-else
          :src="generateDesktopUserAvatar()"
          :alt="displayInfo.name"
          class="avatar-image"
        />
      </div>
    </button>

    <!-- Desktop Dropdown Menu -->
    <Transition name="dropdown-slide">
      <div v-if="isOpen && !isMobileContext" class="user-dropdown" ref="dropdownRef">
        <div class="dropdown-header">
          <div class="user-info">
            <div class="user-avatar-large">
              <img
                v-if="displayInfo.avatarUrl"
                :src="displayInfo.avatarUrl"
                :alt="displayInfo.name"
                class="avatar-image"
                @error="handleLargeAvatarError"
              />
              <img
                v-else
                :src="generateLargeUserAvatar()"
                :alt="displayInfo.name"
                class="avatar-image"
              />
            </div>
            <div class="user-details">
              <h3 class="user-name">{{ displayInfo.name }}</h3>
              <p class="user-email">{{ displayInfo.email }}</p>
            </div>
          </div>
        </div>

        <div class="menu-content">
          <!-- User Mode Sections -->
          <template v-if="!isStoreMode">
            <!-- Perfil Section -->
            <div class="menu-section">
              <div class="section-header">
                <HugeiconsIcon :icon="UserIcon" size="16" :strokeWidth="1.5" />
                <span class="section-title">{{ t('userMenu.profile') }}</span>
              </div>
              <div class="menu-items">
                <button @click="navigateToProfile" class="menu-item">
                  <HugeiconsIcon :icon="UserAccountIcon" size="18" :strokeWidth="1.5" />
                  <span>{{ t('userMenu.myProfile') }}</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
              </div>
            </div>

            <!-- Configurações Section -->
            <div class="menu-section">
              <div class="section-header">
                <HugeiconsIcon :icon="Settings02Icon" size="16" :strokeWidth="1.5" />
                <span class="section-title">{{ t('userMenu.settings') }}</span>
              </div>
              <div class="menu-items">
                <button @click="navigateToPreferences" class="menu-item">
                  <HugeiconsIcon :icon="Settings02Icon" size="18" :strokeWidth="1.5" />
                  <span>{{ t('userMenu.preferences') }}</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
                <button @click="navigateToAccount" class="menu-item">
                  <HugeiconsIcon :icon="UserAccountIcon" size="18" :strokeWidth="1.5" />
                  <span>{{ t('userMenu.accountSettings') }}</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
              </div>
            </div>

            <!-- Lojas Section -->
            <div class="menu-section">
              <div class="section-header">
                <HugeiconsIcon :icon="Store01Icon" size="16" :strokeWidth="1.5" />
                <span class="section-title">{{ t('userMenu.stores') }}</span>
              </div>
              <div class="menu-items">
                <button @click="navigateToAllStores" class="menu-item">
                  <HugeiconsIcon :icon="Store01Icon" size="18" :strokeWidth="1.5" />
                  <span>{{ t('userMenu.allStores') }}</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
              </div>
            </div>

            <!-- Segurança Section -->
            <div class="menu-section">
              <div class="section-header">
                <HugeiconsIcon :icon="SecurityCheckIcon" size="16" :strokeWidth="1.5" />
                <span class="section-title">{{ t('userMenu.security') }}</span>
              </div>
              <div class="menu-items">
                <button @click="navigateToChangePassword" class="menu-item">
                  <HugeiconsIcon :icon="SquareUnlock02Icon" size="18" :strokeWidth="1.5" />
                  <span>{{ t('userMenu.changePassword') }}</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
              </div>
            </div>
          </template>
          
          <!-- Store Mode Sections -->
          <template v-if="isStoreMode">
            <!-- Equipe Section -->
            <div v-if="canViewTeam" class="menu-section">
              <div class="section-header">
                <HugeiconsIcon :icon="UserGroup02Icon" size="16" :strokeWidth="1.5" />
                <span class="section-title">Equipe</span>
              </div>
              <div class="menu-items">
                <button @click="navigateToTeamManagement" class="menu-item">
                  <HugeiconsIcon :icon="UserGroup02Icon" size="18" :strokeWidth="1.5" />
                  <span>Gestão de Equipe</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
              </div>
            </div>

            <!-- Lojas Section -->
            <div class="menu-section">
              <div class="section-header">
                <HugeiconsIcon :icon="Store01Icon" size="16" :strokeWidth="1.5" />
                <span class="section-title">{{ t('userMenu.stores') }}</span>
              </div>
              <div class="menu-items">
                <button @click="navigateToAllStores" class="menu-item">
                  <HugeiconsIcon :icon="Store01Icon" size="18" :strokeWidth="1.5" />
                  <span>{{ t('userMenu.allStores') }}</span>
                  <ChevronRight class="item-arrow" size="16" :strokeWidth="1.5" />
                </button>
              </div>
            </div>
          </template>
        </div>

        <div class="dropdown-footer">
          <button @click="handleLogout" class="logout-button">
            <HugeiconsIcon :icon="LogoutIcon" size="18" :strokeWidth="1.5" />
            <span>{{ t('userMenu.logout') }}</span>
          </button>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from 'vue'
import { ChevronRight } from 'lucide-vue-next'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserIcon,
  UserAccountIcon,
  Settings02Icon,
  SecurityCheckIcon,
  ShieldKeyIcon,
  SquareUnlock02Icon,
  Store01Icon,
  LogoutIcon,
  UserGroup02Icon
} from '@hugeicons-pro/core-stroke-standard'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth.store'
import { useStoreStore } from '@/stores/store.store'
import { useGlobalPermissions, resetGlobalPermissions } from '@/composables/usePermissions'
import { PERMISSIONS } from '@/constants/permissions'
import { generateUserAvatar, handleImageError } from '@/utils/avatarUtils.js'

// i18n
const { t } = useI18n()

// Router & Store
const router = useRouter()
const authStore = useAuthStore()
const storeStore = useStoreStore()

// Permissions
const { hasPermission } = useGlobalPermissions()

// Props
const props = defineProps({
  mobileContext: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: null, // 'user' | 'store' | null (auto-detect)
    validator: (value) => value === null || ['user', 'store'].includes(value)
  }
})

// Component state
const isOpen = ref(false)
const userMenuRef = ref(null)
const toggleButtonRef = ref(null)
const dropdownRef = ref(null)

// Current user
const currentUser = computed(() => authStore.user)

// Current store
const currentStore = computed(() => storeStore.selectedStore)

// Menu mode detection
const menuMode = computed(() => {
  // If type prop is explicitly set, use it
  if (props.type) {
    return props.type
  }
  
  // Auto-detect based on store token presence
  return authStore.storeToken ? 'store' : 'user'
})

// Check if we're in store mode
const isStoreMode = computed(() => menuMode.value === 'store')

// Permission checks
const canViewTeam = computed(() => hasPermission(PERMISSIONS.TEAM_VIEW))

// Get current display info based on mode
const displayInfo = computed(() => {
  if (isStoreMode.value) {
    return {
      name: currentStore.value?.name || 'Loja',
      email: currentStore.value?.urlIluria || currentStore.value?.url || 'loja.iluria.com',
      icon: Store01Icon,
      avatarUrl: storeStore.getStoreLogo() // Usar logo da loja se disponível
    }
  } else {
    return {
      name: currentUser.value?.name || 'Usuário',
      email: currentUser.value?.email || '<EMAIL>',
      icon: UserIcon,
      avatarUrl: currentUser.value?.profilePictureUrl
    }
  }
})

// Mobile context detection
const isMobileContext = computed(() => {
  // Detectar se está dentro do menu mobile do NavBar
  return props.mobileContext || (userMenuRef.value?.closest('.mobile-menu') !== null)
})

// Scroll detection
const lastScrollY = ref(0)
const scrollThreshold = 10

// Toggle dropdown
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// Close dropdown programmatically
const closeDropdown = () => {
  isOpen.value = false
}

// Navigation methods
const navigateToProfile = () => {
  router.push({ name: 'UserSettings', query: { section: 'profile' } })
  closeDropdown()
}

const navigateToPreferences = () => {
  router.push({ name: 'UserSettings', query: { section: 'preferences' } })
  closeDropdown()
}

const navigateToChangePassword = () => {
  router.push({ name: 'UserSettings', query: { section: 'password' } })
  closeDropdown()
}

const navigateToAccount = () => {
  router.push({ name: 'UserSettings', query: { section: 'account' } })
  closeDropdown()
}

const navigateToTeamManagement = () => {
  router.push({ path: '/team' })
  closeDropdown()
}

const navigateToAllStores = async () => {
  // Limpar a loja selecionada e voltar para a tela de seleção
  storeStore.setSelectedStore(null)
  
  // Reset das permissões globais antes de limpar o token
  resetGlobalPermissions()
  
  await authStore.setStoreToken(null)
  router.push({ name: 'StoreSelection' })
  closeDropdown()
}

// Logout handler
const handleLogout = () => {
  // Reset das permissões globais no logout
  resetGlobalPermissions()
  
  authStore.logout()
  closeDropdown()
}

// Handle scroll to close dropdown when navbar hides
const handleScroll = () => {
  const currentScrollY = window.scrollY
  
  // Calcular direção do scroll
  const scrollDifference = Math.abs(currentScrollY - lastScrollY.value)
  
  // Só atualizar se o scroll foi significativo
  if (scrollDifference < scrollThreshold) return
  
  // Se rolando para baixo e o menu está aberto, fechar
  if (currentScrollY > lastScrollY.value && currentScrollY > 50 && isOpen.value) {
    closeDropdown()
  }
  
  lastScrollY.value = currentScrollY
}

// Close dropdown when clicking outside
const handleClickOutside = (event) => {
  if (userMenuRef.value && !userMenuRef.value.contains(event.target)) {
    isOpen.value = false
  }
}

// Close dropdown on escape key
const handleKeydown = (event) => {
  if (event.key === 'Escape' && isOpen.value) {
    isOpen.value = false
    toggleButtonRef.value?.focus()
  }
}

// Add event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('scroll', handleScroll)
})

// Remove event listeners
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('scroll', handleScroll)
})

// Avatar generation functions
const generateMobileUserAvatar = () => {
  const userName = displayInfo.value.name || ''
  return generateUserAvatar(userName, 56, 'square') // 56px para mobile, quadrado
}

const generateDesktopUserAvatar = () => {
  const userName = displayInfo.value.name || ''
  return generateUserAvatar(userName, 24, 'square') // 24px para desktop, quadrado
}

const generateLargeUserAvatar = () => {
  const userName = displayInfo.value.name || ''
  return generateUserAvatar(userName, 48, 'square') // 48px para dropdown, quadrado
}

// Avatar error handlers
const handleMobileAvatarError = (event) => {
  const userName = displayInfo.value.name || ''
  handleImageError(event, userName, 'user', 'square', 56)
}

const handleDesktopAvatarError = (event) => {
  const userName = displayInfo.value.name || ''
  handleImageError(event, userName, 'user', 'square', 24)
}

const handleLargeAvatarError = (event) => {
  const userName = displayInfo.value.name || ''
  handleImageError(event, userName, 'user', 'square', 48)
}

// Expose method for parent components
defineExpose({
  closeDropdown
})
</script>

<style scoped>
.user-menu {
  position: relative;
  display: flex;
  align-items: center;
}

.user-toggle-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: var(--iluria-color-navbar-fg, #374151);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  outline: none;
  position: relative;
}

.user-toggle-button:hover {
  background: transparent !important;
  transform: translateY(-1px);
}

.user-toggle-button:active {
  transform: translateY(0);
}

.user-toggle-button.active {
  background: rgba(0, 0, 0, 0.1);
  color: var(--iluria-color-primary, #3b82f6);
}

.user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  transition: all 0.2s ease;
}

.user-avatar svg {
  width: 28px;
  height: 28px;
  transition: transform 0.2s ease;
}

.user-avatar .avatar-image {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.user-toggle-button:hover .user-avatar svg,
.user-toggle-button:hover .user-avatar .avatar-image {
  transform: scale(1.1);
}



/* Dropdown */
.user-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 280px;
  background: var(--iluria-color-container-bg, #f9fafb);
  border: 1px solid var(--iluria-color-border, #e5e7eb);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1)), 
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.dropdown-header {
  padding: 16px;
  border-bottom: 1px solid var(--iluria-color-border, #e5e7eb);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar-large {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  background: var(--iluria-color-primary, #3b82f6);
  border-radius: 14px;
  color: white;
  flex-shrink: 0;
  overflow: hidden;
}

.user-avatar-large .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 14px;
}

/* Store mode avatar styling */
.user-menu.store-mode .user-avatar-large {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.user-menu.store-mode .mobile-user-avatar {
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.user-details {
  flex: 1;
  min-width: 0;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary, #1f2937);
  margin: 0 0 2px 0;
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-email {
  font-size: 12px;
  color: var(--iluria-color-text-secondary, #6b7280);
  margin: 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.menu-content {
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;
}

.menu-section {
  margin-bottom: 16px;
}

.menu-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  margin-bottom: 4px;
}

.section-title {
  font-size: 11px;
  font-weight: 600;
  color: var(--iluria-color-text-muted, #9ca3af);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.menu-items {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--iluria-color-text-primary, #1f2937);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  outline: none;
}

.menu-item:hover {
  background: var(--iluria-color-hover, rgba(0, 0, 0, 0.05));
  transform: translateX(2px);
}

.menu-item span {
  flex: 1;
}

.item-arrow {
  color: var(--iluria-color-text-muted, #9ca3af);
  transition: all 0.2s ease;
}

.menu-item:hover .item-arrow {
  color: var(--iluria-color-text-secondary, #6b7280);
  transform: translateX(2px);
}

.dropdown-footer {
  padding: 8px;
  border-top: 1px solid var(--iluria-color-border, #e5e7eb);
}

.logout-button {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--iluria-color-danger, #dc2626);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-size: 14px;
  font-weight: 500;
  outline: none;
}

.logout-button:hover {
  background: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.1));
  transform: translateX(2px);
}

/* Dropdown slide animation */
.dropdown-slide-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dropdown-slide-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.dropdown-slide-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-slide-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.98);
}

/* Scroll styling for menu content */
.menu-content::-webkit-scrollbar {
  width: 4px;
}

.menu-content::-webkit-scrollbar-track {
  background: transparent;
}

.menu-content::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border, #e5e7eb);
  border-radius: 2px;
}

.menu-content::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-border-hover, #d1d5db);
}

/* Mobile Context Styles */
.mobile-context {
  width: 100%;
}

.mobile-user-section {
  width: 100%;
}

.mobile-user-header {
  padding: 16px 0;
  border-bottom: 1px solid var(--iluria-color-border, #e5e7eb);
  margin-bottom: 16px;
}

.mobile-user-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.mobile-user-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: var(--iluria-color-primary, #3b82f6);
  border-radius: 18px;
  color: white;
  flex-shrink: 0;
  overflow: hidden;
}

.mobile-user-avatar .avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 18px;
}

.mobile-user-details {
  flex: 1;
  min-width: 0;
}

.mobile-user-name {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary, #1f2937);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.mobile-user-email {
  font-size: 14px;
  color: var(--iluria-color-text-secondary, #6b7280);
  margin: 0;
  line-height: 1.3;
}

.mobile-menu-items {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.mobile-menu-item {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 16px 0;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: var(--iluria-color-text-primary, #1f2937);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  outline: none;
}

.mobile-menu-item:hover {
  background: var(--iluria-color-hover, rgba(0, 0, 0, 0.05));
  transform: translateX(4px);
}

.mobile-menu-item span {
  flex: 1;
}

.mobile-item-arrow {
  color: var(--iluria-color-text-muted, #9ca3af);
  transition: all 0.2s ease;
}

.mobile-menu-item:hover .mobile-item-arrow {
  color: var(--iluria-color-text-secondary, #6b7280);
  transform: translateX(4px);
}

.mobile-logout-button {
  display: flex;
  align-items: center;
  gap: 16px;
  width: 100%;
  padding: 16px 0;
  margin-top: 8px;
  border: none;
  border-radius: 12px;
  background: transparent;
  color: var(--iluria-color-danger, #dc2626);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  font-size: 16px;
  font-weight: 500;
  outline: none;
  border-top: 1px solid var(--iluria-color-border, #e5e7eb);
  padding-top: 24px;
}

.mobile-logout-button:hover {
  background: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.1));
  transform: translateX(4px);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .user-dropdown {
    width: 250px;
    right: -8px;
  }

  .user-toggle-button {
    padding: 8px 10px;
  }

  .user-avatar {
    width: 32px;
    height: 32px;
  }
  
  .user-avatar svg {
    width: 24px;
    height: 24px;
  }
  
  .user-avatar .avatar-image {
    width: 28px;
    height: 28px;
    border-radius: 5px;
  }
}

/* Dark theme adjustments */
.theme-dark .user-dropdown {
  background: #1a1a1a;
  border-color: #333333;
}

.theme-dark .dropdown-header {
  border-bottom-color: #333333;
}

.theme-dark .menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.theme-dark .logout-button:hover {
  background: rgba(220, 38, 38, 0.2);
}

.theme-dark .dropdown-footer {
  border-top-color: #333333;
}

.theme-dark .menu-content::-webkit-scrollbar-thumb {
  background: #444444;
}

.theme-dark .menu-content::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Dark theme for mobile */
.theme-dark .mobile-user-header {
  border-bottom-color: #333333;
}

.theme-dark .mobile-menu-item:hover {
  background: rgba(255, 255, 255, 0.05);
}

.theme-dark .mobile-logout-button {
  border-top-color: #333333;
}

.theme-dark .mobile-logout-button:hover {
  background: rgba(220, 38, 38, 0.2);
}
</style>