<template>
    <nav class="w-full py-1 shadow-sm border-b fixed top-0 left-0 right-0 z-50 transition-transform duration-300 ease-in-out navbar-themed"
        :class="{
            'translate-y-0': isNavbarVisible,
            '-translate-y-full': !isNavbarVisible,
        }" :style="{
            backgroundColor: 'var(--iluria-color-navbar-bg)',
            borderBottomColor: 'var(--iluria-color-border)',
            color: 'var(--iluria-color-navbar-fg)',
        }">
        <div class="flex items-center h-16 justify-between px-6 w-full">
            <!-- Logo -->
            <RouterLink to="/" class="ml-0 md:ml-16">
                <IluriaLogo height="3rem" width="auto" class="h-12 md:h-14 w-auto logo-themed" />
            </RouterLink>

            <!-- Desktop Navigation -->
            <div class="hidden md:flex md:space-x-8">
                <div v-for="(item, index) in filteredNavigation" :key="index" class="relative">
                    <!-- Main Menu Options -->
                    <button v-if="item.columns.length > 0" @click="toggleMenu(item.id)" class="nav-button"
                        :class="{ 'nav-button-active': activeMenu === item.id }">
                        {{ item.name }}
                        <div class="ml-1 w-4 h-4 flex items-center justify-center relative">
                            <transition enter-active-class="transition-all duration-200 ease-out"
                                leave-active-class="transition-all duration-200 ease-out" enter-from-class="opacity-0"
                                enter-to-class="opacity-100" leave-from-class="opacity-100" leave-to-class="opacity-0"
                                mode="out-in">
                                <ChevronDown v-if="activeMenu !== item.id" :key="'chevron'" class="w-4 h-4 icon-desktop"
                                    :strokeWidth="2.5" />
                                <HugeiconsIcon v-else :key="'minus'" :icon="MinusSignIcon" :size="16" :strokeWidth="2"
                                    class="icon-desktop" />
                            </transition>
                        </div>
                    </button>

                    <RouterLink v-else :to="item.href" class="nav-button">
                        {{ item.name }}
                    </RouterLink>

                    <!-- Mega Menu Panel -->
                    <transition enter-active-class="transition ease-out duration-400"
                        enter-from-class="opacity-0 translate-y-1" enter-to-class="opacity-100 translate-y-0"
                        leave-active-class="transition ease-in duration-150"
                        leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 translate-y-1">
                        <div v-if="
                            activeMenu === item.id &&
                            item.columns.length > 0
                        " :id="`menu-${item.id}`"
                            class="absolute top-full mt-4 mega-menu rounded-lg shadow-lg z-50 overflow-hidden left-1/2 transform -translate-x-1/2"
                            :class="[
                                item.columns.length === 1
                                    ? 'w-64'
                                    : 'w-screen max-w-2xl',
                            ]">
                            <div class="p-4">
                                <div class="grid gap-8" :class="[
                                    item.columns.length === 1
                                        ? 'grid-cols-1'
                                        : 'grid-cols-2',
                                ]">
                                    <div v-for="(
column, colIndex
                                        ) in item.columns" :key="colIndex" class="space-y-4">
                                        <div v-for="(
subItem, subIndex
                                            ) in column" :key="subIndex" class="menu-item">
                                            <a :href="subItem.href" class="menu-link">
                                                <div class="flex-shrink-0">
                                                    <!-- component :is="subItem.icon" class="w-6 h-6 text-black" / -->
                                                    <component v-if="subItem.icon" :is="HugeiconsIcon"
                                                        :icon="subItem.icon" class="w-6 icon-desktop"
                                                        :color="subItem.color" size="24" />
                                                </div>
                                                <div class="ml-4">
                                                    <p class="menu-link-title">
                                                        {{ subItem.name }}
                                                    </p>
                                                    <p class="menu-link-description">
                                                        {{
                                                            subItem.description
                                                        }}
                                                    </p>
                                                </div>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </transition>
                </div>
            </div>

            <!-- Desktop: Search, Profile, Notifications -->
            <div class="hidden md:flex mr-16" style="color: var(--iluria-color-navbar-fg)">
                <div class="flex items-center justify-end">
                    <div class="flex items-center space-x-3">
                        <button class="navbar-button navbar-search-button" @click="openSearchModal" :title="t('screenSearch.placeholderShort')">
                            <HugeiconsIcon :icon="Search02Icon" size="24" :strokeWidth="1.5" class="icon-desktop" />
                        </button>
                        <button class="navbar-button">
                            <HugeiconsIcon :icon="MenuSquareIcon" size="24" :strokeWidth="1.5" class="icon-desktop" />
                        </button>
                        <NotificationBell type="store" :store-id="storeStore.getSelectedStoreId()" />
                        <ThemeSelector />
                        <UserMenu />
                    </div>
                </div>
            </div>

            <!-- Mobile: Icons + Menu button -->
            <div class="md:hidden flex items-center space-x-2 mr-2">
                <button class="navbar-button-mobile navbar-search-button-mobile" @click="openSearchModal" :title="t('screenSearch.placeholderShort')">
                    <HugeiconsIcon :icon="Search02Icon" size="24" :strokeWidth="1.5" class="icon-mobile" />
                </button>
                <NotificationBell type="store" :store-id="storeStore.getSelectedStoreId()" />
                <ThemeSelector />
                <button @click="toggleMobileMenu" class="navbar-button-mobile">
                    <Menu v-if="!mobileMenuOpen" class="w-6 h-6 icon-mobile" />
                    <X v-else class="w-6 h-6 icon-mobile" />
                </button>
            </div>
        </div>
    </nav>

    <!-- Mobile menu fullscreen -->
    <transition enter-active-class="transition-all ease-out duration-300" enter-from-class="opacity-0"
        enter-to-class="opacity-100" leave-active-class="transition-all ease-in duration-200"
        leave-from-class="opacity-100" leave-to-class="opacity-0">
        <div v-if="mobileMenuOpen" class="md:hidden fixed inset-0 z-60 mobile-menu flex flex-col">
            <!-- Header do menu mobile -->
            <div class="mobile-menu-header flex-shrink-0">
                <div class="flex items-center justify-between p-6">
                    <IluriaLogo height="2.5rem" width="auto" class="h-10 w-auto logo-themed" />
                    <button @click="closeMobileMenu" class="mobile-close-button">
                        <X class="w-6 h-6 icon-mobile" />
                    </button>
                </div>
                
                <!-- User/Store Info -->
                <div class="px-6 pb-4">
                    <div class="mobile-user-info">
                        <div class="mobile-user-avatar" :class="{ 'store-mode': authStore.storeToken }">
                            <!-- Mostrar logo da loja se disponível, senão mostrar ícone -->
                            <img
                                v-if="authStore.storeToken && storeStore.getStoreLogo()"
                                :src="storeStore.getStoreLogo()"
                                alt="Logo da Loja"
                                class="store-logo-image"
                            />
                            <HugeiconsIcon
                                v-else
                                :icon="authStore.storeToken ? Store01Icon : UserIcon"
                                size="24"
                                :strokeWidth="1.5"
                            />
                        </div>
                        <div class="mobile-user-details">
                            <h3 class="mobile-user-name">
                                {{ authStore.storeToken ? (storeStore.selectedStore?.name || 'Loja') : (authStore.user?.name || 'Usuário') }}
                            </h3>
                            <p class="mobile-user-email">
                                {{ authStore.storeToken ? (storeStore.selectedStore?.urlIluria || storeStore.selectedStore?.url || 'loja.iluria.com') : (authStore.user?.email || '<EMAIL>') }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Conteúdo do menu com scroll próprio -->
            <div class="flex-1 overflow-y-auto">
                <div class="p-6">
                    <!-- Navegação principal -->
                    <div class="space-y-2">
                        <div v-for="(item, index) in filteredNavigation" :key="index">
                            <button @click="
                                mobileActiveMenu =
                                mobileActiveMenu === item.id
                                    ? null
                                    : item.id
                                " class="mobile-menu-button" :class="{
                                    active: mobileActiveMenu === item.id,
                                }">
                                {{ item.name }}
                                <div class="w-6 h-6 flex items-center justify-center relative">
                                    <transition enter-active-class="transition-all duration-200 ease-out"
                                        leave-active-class="transition-all duration-200 ease-out"
                                        enter-from-class="opacity-0" enter-to-class="opacity-100"
                                        leave-from-class="opacity-100" leave-to-class="opacity-0" mode="out-in">
                                        <ChevronDown v-if="mobileActiveMenu !== item.id" :key="'chevron-mobile'"
                                            class="w-5 h-5 text-gray-600 icon-mobile" :strokeWidth="2.5" />
                                        <HugeiconsIcon v-else :key="'minus-mobile'" :icon="MinusSignIcon" :size="20"
                                            :strokeWidth="2" class="text-gray-600 icon-mobile" />
                                    </transition>
                                </div>
                            </button>
                            <transition enter-active-class="transition-all ease-out duration-300"
                                enter-from-class="opacity-0 max-h-0" enter-to-class="opacity-100 max-h-[800px]"
                                leave-active-class="transition-all ease-in duration-200"
                                leave-from-class="opacity-100 max-h-[800px]" leave-to-class="opacity-0 max-h-0">
                                <div v-if="mobileActiveMenu === item.id" class="ml-4 mt-2 space-y-1 overflow-hidden">
                                    <template v-for="(
column, colIndex
                                        ) in item.columns" :key="colIndex">
                                        <component
                                            v-for="(subItem, subIndex) in column"
                                            :key="subIndex"
                                            :is="subItem.action === 'logout' ? 'button' : 'a'"
                                            :href="subItem.action !== 'logout' ? subItem.href : undefined"
                                            @click="subItem.action === 'logout' ? handleLogout() : undefined"
                                            class="mobile-submenu-link"
                                            :class="{ 'logout-item': subItem.action === 'logout' }"
                                        >
                                            <component v-if="subItem.icon" :is="HugeiconsIcon" :icon="subItem.icon"
                                                :size="20" class="mr-3 flex-shrink-0 icon-mobile"
                                                :color="subItem.color" />
                                            <div>
                                                <div class="mobile-submenu-title">
                                                    {{ subItem.name }}
                                                </div>
                                                <div class="mobile-submenu-description">
                                                    {{ subItem.description }}
                                                </div>
                                            </div>
                                        </component>
                                    </template>
                                </div>
                            </transition>
                        </div>
                    </div>

                    <!-- Separador -->
                    <div class="my-6 border-t" style="border-color: var(--iluria-color-border);"></div>

                    <!-- Botões finais -->
                    <div class="mt-auto pt-4 space-y-2">
                        <!-- Todas as lojas -->
                        <button @click="navigateToAllStores" class="mobile-simple-button">
                            <HugeiconsIcon :icon="Store01Icon" size="20" :strokeWidth="1.5" />
                            <span>Todas as lojas</span>
                        </button>

                        <!-- Botão de Logout -->
                        <button @click="handleLogout" class="mobile-simple-button logout-style">
                            <HugeiconsIcon :icon="LogoutIcon" size="20" :strokeWidth="1.5" />
                            <span>Sair</span>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </transition>

    <!-- Modal de pesquisa de telas -->
    <ScreenSearchModal v-model="searchModalVisible" />
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, nextTick } from "vue";
import {
    Grip,
    SearchX,
    SquareArrowDown,
    Menu,
    X,
    ChevronDown,
    ShoppingCart,
    Search,
    User,
    LayoutGrid,
    Bell,
} from "lucide-vue-next";

import { HugeiconsIcon } from "@hugeicons/vue";
import {
    Image02Icon,
    InvoiceIcon,
    DiscountTag01Icon,
    AnalysisTextLinkIcon,
    DollarSquareIcon,
    GiftIcon,
    LeftToRightListBulletIcon,
    DashboardSquare03Icon,
    FilterIcon,
    PackageAddIcon,
    SearchList01Icon,
    MessageUser02Icon,
    Bookmark02Icon,
    ArrangeIcon,
    MessageMultiple01Icon,
    MailLove01Icon,
    StoreLocation02Icon,
    HierarchyFilesIcon,
    UserGroupIcon,
    ShippingTruck01Icon,
    Target02Icon,
    Payment02Icon,
    InternetIcon,
    CardExchange02Icon,
    BeachIcon,
    Mailbox01Icon,
    CouponPercentIcon,
    TapeMeasureIcon,
    LicenseDraftIcon,
    PromotionIcon,
    Coupon01Icon,
    AiBookIcon,
} from "@hugeicons-pro/core-stroke-rounded";
import {
    Notification01Icon,
    File01Icon,
    MenuSquareIcon,
    Search02Icon,
    PaintBoardIcon,
    MinusSignIcon,
    BookOpen01Icon,
    CreditCardIcon,
    LogoutIcon,
    UserIcon,
    Settings02Icon,
    SecurityCheckIcon,
    Store01Icon,
} from "@hugeicons-pro/core-stroke-standard";
import ThemeSelector from "@/components/layout/ThemeSelector.vue";
import UserMenu from "@/components/layout/UserMenu.vue";
import IluriaLogo from "@/components/iluria/IluriaLogo.vue";
import ScreenSearchModal from "@/components/layout/ScreenSearchModal.vue";
import NotificationBell from "@/components/layout/NotificationBell.vue";

import { useAuthStore } from "@/stores/auth.store";
import { useStoreStore } from "@/stores/store.store";
import { useRouter } from "vue-router";
import { useI18n } from 'vue-i18n';
import { useGlobalPermissions, resetGlobalPermissions } from "@/composables/usePermissions";
import { PERMISSIONS } from "@/constants/permissions";

const { t } = useI18n();
const { hasPermission, hasAnyPermission, permissionsLoaded, userPermissions } = useGlobalPermissions();

// Estado para forçar re-renderização da navbar
const navbarKey = ref(0);

const activeMenu = ref(null);
const mobileMenuOpen = ref(false);
const mobileActiveMenu = ref(null);
const searchModalVisible = ref(false);
const authStore = useAuthStore();
const storeStore = useStoreStore();
const router = useRouter();

// Computed para obter o storeId
const currentStoreId = computed(() => {
  const storeId = storeStore.getSelectedStoreId();
  return storeId;
});

// Computed para verificar se temos uma store selecionada
const hasValidStore = computed(() => {
  return currentStoreId.value && currentStoreId.value !== 'unknown';
});


// Watch para detectar mudanças nas permissões e forçar atualização da navbar
watch([userPermissions, permissionsLoaded, () => authStore.storeToken],
  async ([newPermissions, newLoaded, newToken], [oldPermissions, oldLoaded, oldToken]) => {
    // Se mudou o estado de carregamento das permissões ou o token da loja
    if (newLoaded !== oldLoaded || newToken !== oldToken) {

      // Aguarda o próximo tick para garantir que as mudanças sejam processadas
      await nextTick();

      // Força re-renderização incrementando a chave
      navbarKey.value++;
    }

    // Se as permissões mudaram de conteúdo
    if (newPermissions !== oldPermissions && newLoaded) {
      navbarKey.value++;
    }
  },
  { immediate: false, deep: true }
);

// Watch para carregar brand assets quando uma loja for selecionada
watch(() => storeStore.selectedStore, async (newStore, oldStore) => {
  if (newStore && newStore.id && newStore.id !== oldStore?.id) {
    // Carregar brand assets da nova loja
    await storeStore.loadBrandAssets();
  }
}, { immediate: true });

// Scroll direction detection
const isNavbarVisible = ref(true);
const lastScrollY = ref(0);
const scrollThreshold = 10; // pixels

const handleScroll = () => {
    const currentScrollY = window.scrollY;

    // Sempre mostrar no topo da página
    if (currentScrollY < 50) {
        isNavbarVisible.value = true;
        lastScrollY.value = currentScrollY;
        return;
    }

    // Calcular direção do scroll
    const scrollDifference = Math.abs(currentScrollY - lastScrollY.value);

    // Só atualizar se o scroll foi significativo (evita flickering)
    if (scrollDifference < scrollThreshold) return;

    if (currentScrollY > lastScrollY.value) {
        // Rolando para baixo - esconder navbar e fechar menus
        isNavbarVisible.value = false;
        // Fechar apenas menus desktop (não mobile quando aberto)
        activeMenu.value = null;
        // Só fechar menu mobile se não estiver aberto (para permitir scroll interno)
        if (!mobileMenuOpen.value) {
            mobileActiveMenu.value = null;
        }
        // Fechar menu de temas também
        const themeSelector = document.querySelector(".theme-selector");
        if (themeSelector && themeSelector.__vue__) {
            themeSelector.__vue__.closeDropdown?.();
        }
    } else {
        // Rolando para cima - mostrar navbar
        isNavbarVisible.value = true;
    }

    lastScrollY.value = currentScrollY;
};

const navigation = [
    {
        id: "orders",
        name: "Vendas",
        columns: [
            [
                {
                    name: "Pedidos",
                    description:
                        "Confira a lista de pedidos realizados na loja",
                    href: "/orders",
                    icon: InvoiceIcon,
                    color: "#4A90E2",
                    requiredPermissions: [PERMISSIONS.ORDER_VIEW]
                },
                {
                    name: "Cupons de Desconto",
                    description: "Crie e gerencie cupons de desconto",
                    href: "/customer/coupon-manager",
                    icon: DiscountTag01Icon,
                    color: "#7ED321",
                },
                {
                    name: "Pedido mínimo",
                    description:
                        "Configure a loja para ter um valor mínimo de pedido",
                    href: "/settings/minimum-order",
                    icon: DollarSquareIcon,
                    color: "#8B572A",
                },
                {
                    name: "Cartões Presente",
                    description: "Veja cartãos já cadastrados e cadastre novos",
                    href: "/products/gift-card",
                    icon: GiftIcon
                }
            ],
            [
                {
                    name: "Clientes",
                    description: "Acompanhe a lista de clientes da sua loja",
                    href: "/customer-list",
                    icon: UserIcon,
                    color: "#D0021B",
                    requiredPermissions: [PERMISSIONS.CUSTOMER_VIEW]
                },
                {
                    name: "Promoções",
                    description: "Gerenciar promoções e descontos da loja",
                    href: "/promotions",
                    icon: CouponPercentIcon,
                    color: "#F5A623",
                },
                {
                    name: "Relatórios",
                    description: "Acompanhe o desempenho da sua loja",
                    href: "/customers/newOld",
                    icon: AnalysisTextLinkIcon,
                    color: "#BD10E0",
                },
            ],
        ],
    },
    {
        id: "products",
        name: "Produtos",
        columns: [
            [
                {
                    name: "Novo Produto",
                    description: "Crie novos produtos físicos ou digitais",
                    href: "/products/new",
                    icon: PackageAddIcon,
                    color: "#7ED321",
                    requiredPermissions: [PERMISSIONS.PRODUCT_CREATE]
                },
                {
                    name: "Coleções",
                    description: "Cadastre novas coleções de produtos",
                    href: "/product/collection/collection-product-list",
                    icon: DashboardSquare03Icon,
                    color: "#D0021B",
                },
                {
                    name: "Produto Combinado",
                    description: "Combine produtos para criar um produto único",
                    href: "/products/combinado",
                    icon: GiftIcon,
                    color: "#F5A623",
                },
                {
                    name: "Perguntas e Respostas",
                    description:
                        "Gerencie as perguntas e respostas dos produtos",
                    href: "/products/questions-answers",
                    icon: MessageUser02Icon,
                    color: "#8B572A",
                },
                {
                    name: "Categorias",
                    description: "Gerencie as categorias dos produtos",
                    href: "/product/category-manager",
                    icon: LeftToRightListBulletIcon,
                    color: "#4A90E2",
                },
            ],
            [
                {
                    name: "Lista de Produtos",
                    description:
                        "Listagem de produtos que estão disponíveis na loja",
                    href: "/products",
                    icon: SearchList01Icon,
                    color: "#4A90E2",
                    requiredPermissions: [PERMISSIONS.PRODUCT_VIEW]
                },
                {
                    name: "Atributos & Filtros",
                    description: "Gerencie os atributos e filtros dos produtos",
                    href: "/product/attributes-manager",
                    icon: FilterIcon,
                    color: "#BD10E0",
                },
                {
                    name: "Etiquetas",
                    description: "Gerencie as etiquetas dos produtos",
                    href: "/product/label/label-initial",
                    icon: Bookmark02Icon,
                    color: "#B8E986",
                },
                {
                    name: "Tabelas de Medidas",
                    description:
                        "Crie e gerencie tabelas de medidas para produtos",
                    href: "/measurement-tables",
                    icon: TapeMeasureIcon,
                    color: "#9333ea",
                },
                {
                    name: "Organizar Produtos",
                    description:
                        "Defina a ordem de exibição dos produtos na loja",
                    href: "/products/orders",
                    icon: ArrangeIcon,
                    color: "#417505",
                }
            ],
        ],
    },
    {
        id: "marketing",
        name: "Marketing",
        columns: [
            [


                {
                    name: "Comunidade",
                    description:
                        "Gerencie a comunidade de clientes da sua loja",
                    href: "/marketing/community",
                    icon: MessageMultiple01Icon,
                    color: "#8B5CF6",
                },
                {
                    name: "Blog",
                    description: "Gerencie as postagens do blog da sua loja",
                    href: "/blog",
                    icon: LicenseDraftIcon,
                    color: "#50E3C2",
                },
            ],
            [
                {
                    name: "Newsletter",
                    description:
                        "Gerencie inscrições e envie newsletters para seus clientes",
                    href: "/marketing/newsletter",
                    icon: MailLove01Icon,
                    color: "#F59E0B",
                },
                {
                    name: "SEO da página principal",
                    description:
                        "Configurações de SEO da página principal da loja",
                    href: "/settings/store-seo",
                    icon: Target02Icon,
                    color: "#D0021B",
                },
            ],
        ],
    },
    {
        id: "config",
        name: "Configurações",
        columns: [
            [
                {
                    name: "Dados da Loja",
                    description: "Informações gerais da loja e do proprietário",
                    href: "/settings",
                    icon: StoreLocation02Icon,
                    color: "#4A90E2",
                },
                {
                    name: "Frete",
                    description: "Correios, Entrega Local, Descontos e mais ",
                    href: "/settings/shipping",
                    icon: ShippingTruck01Icon,
                    color: "#BD10E0",
                },
                {
                    name: "Dominios",
                    description: "Configurações de domínios da loja",
                    href: "/settings/domain-manager",
                    icon: InternetIcon,
                    color: "#50E3C2",
                },
                {
                    name: "Páginas",
                    description:
                        "Crie, edite ou exclua páginas com conteúdo personalizado",
                    href: "/pages",
                    icon: File01Icon,
                    color: "#4A90E2",
                },
                {
                    name: "CEP da loja",
                    description: "Defina o CEP de origem para cálculo de frete",
                    href: "/settings/origin-cep",
                    icon: Mailbox01Icon,
                    color: "#417505",
                },
            ],
            [
                {
                    name: "Meios de pagamento",
                    description: "Gerencie os meios de pagamento da loja",
                    href: "/settings/payment-methods",
                    icon: Payment02Icon,
                    color: "#417505",
                },
                {
                    name: "Redes Sociais",
                    description:
                        "Configurações do Facebook, Instagram, X, TikTok",
                    href: "/settings/social-media",
                    icon: UserGroupIcon,
                    color: "#F5A623",
                },
                {
                    name: "Notificações por Email",
                    description:
                        "Configure as notificações que você deseja receber por email",
                    href: "/settings/email-notifications",
                    icon: Notification01Icon,
                    color: "#3B82F6",
                },
                {
                    name: "Redirecionamentos",
                    description:
                        "Gerencie os redirecionamentos de URLs da loja",
                    href: "/settings/url-redirect",
                    icon: CardExchange02Icon,
                    color: "#417505",
                },
                {
                    name: "Férias e Manutenção",
                    description:
                        "Ative ou desative os modos de manutenção ou férias",
                    href: "/settings/store-mode",
                    icon: BeachIcon,
                    color: "#F5A623",
                },
                {
                    name: "Gestão de Equipe",
                    description: "Gerencie usuários e permissões da equipe",
                    href: "/team",
                    icon: UserGroupIcon,
                    color: "#4A90E2",
                    requiredPermissions: [PERMISSIONS.TEAM_VIEW]
                },
            ],
        ],
    },

    {
        id: "layout",
        name: "Layout",
        columns: [
            [
                {
                    name: "Layout",
                    description:
                        "Gerencie temas, personalize cores, fontes, imagens e componentes",
                    href: "/themes",
                    icon: PaintBoardIcon,
                    color: "#417505",
                },
                {
                    name: "Gerenciamento de arquivos",
                    description:
                        "Gerencie arquivos e pastas usados no layout da loja",
                    href: "/filemanager",
                    icon: HierarchyFilesIcon,
                    color: "#BD10E0",
                },
            ],
            [
                {
                    name: "Logo & Favicon",
                    description:
                        "Configure as imagens do logo e favicon da loja",
                    href: "/settings/brand-assets",
                    icon: Image02Icon,
                    color: "#3B82F6",
                },
            ],
        ],
    },
    
];

/**
 * Filtra a navigation baseado nas permissões do usuário
 */
const filteredNavigation = computed(() => {
  // Inclui navbarKey.value para forçar recalculo quando necessário
  const _forceRecompute = navbarKey.value;
  
  if (!permissionsLoaded.value) {
    // Se as permissões ainda não carregaram, mostra a navigation vazia por segurança
    return [];
  }
  
  return navigation.map(section => {
    // Filtra as colunas da seção
    const filteredColumns = section.columns.map(column => {
      // Filtra os itens da coluna baseado nas permissões
      return column.filter(item => {
        // Se o item não tem permissões definidas, mostra sempre
        if (!item.requiredPermissions || item.requiredPermissions.length === 0) {
          return true;
        }
        
        // Verifica se o usuário tem qualquer uma das permissões necessárias
        return hasAnyPermission(item.requiredPermissions);
      });
    }).filter(column => column.length > 0); // Remove colunas vazias
    
    // Só inclui a seção se ela tem pelo menos uma coluna com itens
    if (filteredColumns.length > 0) {
      return {
        ...section,
        columns: filteredColumns
      };
    }
    
    return null;
  }).filter(Boolean); // Remove seções null
});

// Add click-outside detection
const closeMenus = (event) => {
    const target = event.target;
    if (!target.closest("button") && !target.closest('[id^="menu-"]')) {
        activeMenu.value = null;
    }
};

// Add menu toggle function
const toggleMenu = (menuId) => {
    activeMenu.value = activeMenu.value === menuId ? null : menuId;
};

// Mobile menu scroll control
const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value;

    if (mobileMenuOpen.value) {
        // Bloquear scroll do body
        document.body.style.overflow = "hidden";
    } else {
        // Restaurar scroll do body
        document.body.style.overflow = "";
        // Fechar submenus também
        mobileActiveMenu.value = null;
    }
};

const closeMobileMenu = () => {
    mobileMenuOpen.value = false;
    document.body.style.overflow = "";
    mobileActiveMenu.value = null;
};

const navigateToAllStores = async () => {
    // Limpar a loja selecionada e voltar para a tela de seleção
    storeStore.setSelectedStore(null);
    
    // Reset das permissões globais antes de limpar o token
    resetGlobalPermissions();
    
    await authStore.setStoreToken(null);
    router.push({ name: 'StoreSelection' });
    closeMobileMenu();
};

const handleLogout = () => {
    authStore.logout();
    closeMobileMenu();
};

// Função para abrir o modal de pesquisa
const openSearchModal = () => {
    searchModalVisible.value = true;
};

// Handler para atalhos de teclado
const handleKeyboardShortcuts = (event) => {
    // Ctrl/Cmd + K para abrir busca
    if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
        event.preventDefault();
        openSearchModal();
    }
};

// Add event listeners
onMounted(() => {
    document.addEventListener("click", closeMenus);
    document.addEventListener("keydown", handleKeyboardShortcuts);
    window.addEventListener("scroll", handleScroll, { passive: true });
    lastScrollY.value = window.scrollY;
});

onUnmounted(() => {
    document.removeEventListener("click", closeMenus);
    document.removeEventListener("keydown", handleKeyboardShortcuts);
    window.removeEventListener("scroll", handleScroll);
    // Garantir que o scroll do body seja restaurado
    document.body.style.overflow = "";
});
</script>

<style scoped>
/* Navbar theming */
.navbar-themed {
    transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
        color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.navbar-button {
    background: transparent;
    border: none;
    padding: 8px;
    border-radius: 6px;
    color: var(--iluria-color-navbar-fg);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-button:hover {
    background-color: var(--iluria-color-navbar-hover, rgba(0, 0, 0, 0.05));
    transform: translateY(-1px);
}

.navbar-button-mobile {
    background: transparent;
    border: none;
    padding: 6px;
    border-radius: 6px;
    color: var(--iluria-color-navbar-fg);
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
}

.navbar-button-mobile:hover {
    background-color: var(--iluria-color-navbar-hover, rgba(0, 0, 0, 0.05));
}

/* Enhanced menu item animations */
.menu-item {
    opacity: 0;
    transform: translateY(10px);
    transition: all 0.3s cubic-bezier(0.25, 0.1, 0.25, 1);
}

.menu-item:nth-child(1) {
    transition-delay: 50ms;
}

.menu-item:nth-child(2) {
    transition-delay: 100ms;
}

.menu-item:nth-child(3) {
    transition-delay: 150ms;
}

/* When menu is visible, show items with a smooth entrance */
[id^="menu-"] .menu-item {
    opacity: 1;
    transform: translateY(0);
}

/* Smooth hover effect for menu items */
.menu-item a {
    transition: background-color 0.2s ease, transform 0.2s ease;
}

.menu-item a:hover {
    transform: translateX(5px);
}

/* Smooth transition for mobile menu items */
.md\:hidden a,
.md\:hidden button {
    transition: background-color 0.2s ease, color 0.2s ease;
}

/* Adjust spacing for single-column menus */
.grid-cols-1 .menu-item:not(:last-child) {
    margin-bottom: 0rem;
}

.grid-cols-1 .menu-item a {
    padding: 0.75rem;
}

/* Otimizações avançadas para renderização nítida */
img[src*="logo-iluria"],
.logo-themed {
    transition: transform 0.15s ease;
    /* Forçar aceleração de hardware */
    transform: translateZ(0);
    will-change: transform;
    /* Otimizações para SVG em alta densidade */
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    image-rendering: pixelated;
    shape-rendering: geometricPrecision;
    /* Anti-aliasing específico */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    /* Evitar blur em transforms */
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

img[src*="logo-iluria"]:hover,
.logo-themed:hover {
    transform: scale(1.1) translateZ(0);
}

/* Otimizações para ícones desktop (suave) */
.icon-desktop {
    shape-rendering: geometricPrecision;
    image-rendering: auto;
    transform: translateZ(0);
    will-change: transform;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Otimizações para ícones mobile (mais agressivas) */
.icon-mobile {
    shape-rendering: crispEdges;
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    transform: translateZ(0);
    will-change: transform;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Otimizações específicas para ícones gerais */
.icon-crisp {
    transform: translateZ(0);
    will-change: transform;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* Classe específica para logo */
.logo-crisp {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
    shape-rendering: geometricPrecision;
    transform: translateZ(0);
    will-change: transform;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

/* Media query para displays de alta densidade */
@media (-webkit-min-device-pixel-ratio: 2),
(min-resolution: 192dpi) {

    img[src*="logo-iluria"],
    .logo-crisp {
        image-rendering: -webkit-optimize-contrast;
        shape-rendering: geometricPrecision;
    }

    .icon-mobile {
        shape-rendering: geometricPrecision;
        image-rendering: -webkit-optimize-contrast;
    }

    .icon-desktop {
        shape-rendering: geometricPrecision;
        image-rendering: auto;
    }
}

/* Mega Menu theming */
.mega-menu {
    background: var(--iluria-color-surface);
    border: 1px solid var(--iluria-color-border);
    color: var(--iluria-color-text);
    box-shadow: var(--iluria-shadow-lg);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-link {
    display: flex;
    align-items: flex-start;
    padding: 0.75rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    color: var(--iluria-color-text);
}

.menu-link:hover {
    background: var(--iluria-color-hover);
}

.menu-link-title {
    font-size: 0.875rem;
    font-weight: 500;
    color: var(--iluria-color-text-primary);
    transition: color 0.3s ease;
}

.menu-link-description {
    margin-top: 0.25rem;
    font-size: 0.875rem;
    color: var(--iluria-color-text-secondary);
    transition: color 0.3s ease;
}

@import "tailwindcss";

.nav-button {
    @apply cursor-pointer relative py-2 px-4 z-2;
    @apply inline-flex items-center;
    @apply justify-center focus:outline-none my-auto;
    @apply rounded-lg;
    @apply text-[var(--iluria-color-navbar-fg)];
    @apply font-medium;
    @apply text-sm;
    @apply transition-all duration-200 ease-in-out;
}

.nav-button:hover {
    background: var(--iluria-color-navbar-hover, rgba(0, 0, 0, 0.05));
}

.nav-button-active {
    background: var(--iluria-color-navbar-active, rgba(0, 0, 0, 0.1));
    color: var(--iluria-color-primary, #3b82f6);
}

.nav-button-active:hover {
    background: var(--iluria-color-navbar-active-hover, rgba(0, 0, 0, 0.15));
}

/* Mobile Menu theming */
.mobile-menu {
    background: var(--iluria-color-surface);
    color: var(--iluria-color-text);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mobile-menu-header {
    border-bottom: 1px solid var(--iluria-color-border);
    transition: border-color 0.3s ease;
}

.mobile-user-info {
    display: flex;
    align-items: center;
    gap: 16px;
}

.mobile-user-avatar {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 56px;
    height: 56px;
    background: var(--iluria-color-primary, #3b82f6);
    border-radius: 16px; /* Quadrado com bordas arredondadas */
    color: white;
    flex-shrink: 0;
    overflow: hidden;
}

.mobile-user-avatar.store-mode {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
}

.store-logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 16px; /* Quadrado com bordas arredondadas */
    padding: 4px;
}

.mobile-user-details {
    flex: 1;
    min-width: 0;
}

.mobile-user-name {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary, #1f2937);
    margin: 0 0 4px 0;
    line-height: 1.2;
}

.mobile-user-email {
    font-size: 14px;
    color: var(--iluria-color-text-secondary, #6b7280);
    margin: 0;
    line-height: 1.3;
}

.mobile-close-button {
    padding: 0.5rem;
    border-radius: 0.375rem;
    color: var(--iluria-color-text);
    transition: all 0.2s ease;
}

.mobile-close-button:hover {
    background: var(--iluria-color-hover);
}

.mobile-menu-button {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0.75rem 1rem;
    font-size: 1.125rem;
    font-weight: 500;
    color: var(--iluria-color-text-primary);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.mobile-menu-button:hover {
    background: var(--iluria-color-hover);
}

.mobile-menu-button.active {
    background: var(--iluria-color-sidebar-bg);
}

.mobile-submenu-link {
    display: flex;
    align-items: center;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    color: var(--iluria-color-text-secondary);
    border-radius: 0.5rem;
    transition: all 0.2s ease;
}

.mobile-submenu-link:hover {
    color: var(--iluria-color-text-primary);
    background: var(--iluria-color-hover);
}

.mobile-submenu-title {
    font-weight: 500;
    transition: color 0.3s ease;
}

.mobile-submenu-description {
    font-size: 0.875rem;
    color: var(--iluria-color-text-muted);
    transition: color 0.3s ease;
}

/* Logout item styling */
.mobile-submenu-link.logout-item {
    color: var(--iluria-color-danger, #dc2626) !important;
    border: none;
    background: transparent;
    width: 100%;
    text-align: left;
    cursor: pointer;
}

.mobile-submenu-link.logout-item:hover {
    background: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.1)) !important;
}

.mobile-submenu-link.logout-item .mobile-submenu-title {
    color: var(--iluria-color-danger, #dc2626);
}

.mobile-submenu-link.logout-item .mobile-submenu-description {
    color: var(--iluria-color-danger-secondary, #b91c1c);
}

/* Search button specific styles - remove active states */
.navbar-search-button,
.navbar-search-button-mobile {
    background: transparent !important;
}

.navbar-search-button:focus,
.navbar-search-button:active,
.navbar-search-button-mobile:focus,
.navbar-search-button-mobile:active {
    background: var(--iluria-color-navbar-hover, rgba(0, 0, 0, 0.05)) !important;
    outline: none;
    box-shadow: none;
}

/* Padronizar todos os botões da navbar para ficarem iguais à lupa - apenas translateY(-1px) no hover */
.navbar-button:hover {
    background: transparent !important;
    transform: translateY(-1px);
}

.navbar-button-mobile:hover {
    background: transparent !important;
}

/* Separadores e links extras no menu mobile */
.mobile-menu .my-8.border-t.border-gray-200 {
    border-color: var(--iluria-color-border) !important;
}

.mobile-menu a[href="/docs"],
.mobile-menu a[href="/pricing"] {
    color: var(--iluria-color-text-primary) !important;
    background: transparent !important;
    transition: all 0.2s ease !important;
}

.mobile-menu a[href="/docs"]:hover,
.mobile-menu a[href="/pricing"]:hover {
    background: var(--iluria-color-hover) !important;
}

/* Dark theme adjustments for mobile user info */
.theme-dark .mobile-menu-header {
  border-bottom-color: #333333;
}

/* Novos estilos para os botões simples */
.mobile-simple-button {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  width: 100%;
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
  outline: none;
  background: transparent;
  color: var(--iluria-color-text-primary);
  gap: 12px;
}

.mobile-simple-button:hover {
  background: var(--iluria-color-hover);
}

.mobile-simple-button.logout-style {
  color: var(--iluria-color-danger, #dc2626);
}

.mobile-simple-button.logout-style:hover {
  background: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.1));
}
</style>
