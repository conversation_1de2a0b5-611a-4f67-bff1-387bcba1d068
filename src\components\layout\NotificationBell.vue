<template>
  <div class="notification-bell">
    <button
      class="navbar-button"
      @click="toggleNotifications"
      :aria-label="$t('notifications.bellAriaLabel')"
      :title="$t('notifications.bellAriaLabel')"
      :class="{ 'nav-button-active': showNotifications }"
    >
      <HugeiconsIcon :icon="Notification01Icon" :size="24" :strokeWidth="1.5" class="icon-desktop" :class="{ 'has-notifications': hasUnreadNotifications }" />
      <Transition name="badge-scale">
        <span v-if="unreadCount > 0" class="notification-badge" :class="{ 'pulse': hasNewNotifications }">
          {{ formatBadgeCount(unreadCount) }}
        </span>
      </Transition>
    </button>
      <!-- Chevron removed, no chevron or button here -->

    <!-- Overlay -->
    <div 
      v-if="showNotifications" 
      class="notification-overlay"
      @click="closeNotifications"
    ></div>

    <!-- Notification Dropdown -->
    <Transition name="dropdown-slide" appear>
      <div 
        v-if="showNotifications" 
        class="notification-dropdown"
        @click.stop
      >
      <div class="notification-header">
        <h3 class="notification-title">
          {{ props.type === 'user' ? $t('notifications.userTitle', 'Personal Notifications') : $t('notifications.storeTitle', 'Store Notifications') }}
        </h3>
        <div class="notification-actions">
          <IluriaButton
            v-if="unreadCount > 0"
            class="action-button mark-read-btn"
            @click="markAllAsRead"
            size="small"
            :disabled="markingAllAsRead"
            :title="$t('notifications.markAllRead')"
          >
            <HugeiconsIcon :icon="CheckmarkCircle02Icon" :size="16" />
          </IluriaButton>
          <IluriaButton
            v-if="notifications.length > 0"
            class="action-button bulk-delete-btn"
@click="confirmBulkDelete"
            size="small"
            :disabled="bulkDeleting"
            :title="$t('notifications.deleteAll')"
          >
            <HugeiconsIcon :icon="RowDeleteIcon" :size="16" />
          </IluriaButton>
          <IluriaButton
            class="action-button settings-btn"
            @click="goToSettings"
            size="small"
            :aria-label="$t('notifications.settings')"
          >
            <HugeiconsIcon :icon="Settings02Icon" :size="16" />
          </IluriaButton>
        </div>
      </div>

      <div class="notification-content">
        <!-- Loading State -->
        <div v-if="loading" class="notification-loading">
          <div class="loading-spinner"></div>
          <p>{{ $t('notifications.loading') }}</p>
        </div>

        <!-- Empty State -->
        <div v-else-if="notifications.length === 0" class="notification-empty">
          <HugeiconsIcon :icon="Notification01Icon" :size="48" class="empty-icon" />
          <h4>{{ $t('notifications.empty.title') }}</h4>
          <p>{{ $t('notifications.empty.message') }}</p>
        </div>

        <!-- Notifications List -->
        <div v-else class="notification-list">
          <TransitionGroup name="notification-item" appear>
            <div
              v-for="(notification, index) in notifications"
              :key="notification.id"
              class="notification-item"
              :class="{ 
                'unread': !notification.isRead,
                'critical': notification.notificationType === 'SECURITY_ALERTS'
              }"
              :style="{ '--delay': `${index * 50}ms` }"
              @click="handleNotificationClick(notification)"
            >
            <div class="notification-icon">
              <HugeiconsIcon 
                :icon="getNotificationIcon(notification.notificationType)" 
                :size="20" 
                :class="getNotificationIconClass(notification.notificationType)"
              />
            </div>
            <div class="notification-body">
              <h5 class="notification-subject">{{ notification.title }}</h5>
              <p class="notification-message">{{ notification.content }}</p>
              <span class="notification-time">{{ formatTime(notification.createdAt) }}</span>
            </div>
            
            <!-- Individual Action Buttons -->
            <div class="notification-actions-individual">
              <!-- Mark as Read Button (only show for unread) -->
              <IluriaButton
                v-if="!notification.isRead"
                variant="ghost"
                size="small"
                class="action-btn mark-read-individual-btn"
                @click.stop="markNotificationAsRead(notification)"
                :disabled="notification.processing"
                :aria-label="$t('notifications.markRead')"
              >
                <HugeiconsIcon :icon="CheckmarkCircle02Icon" :size="16" />
              </IluriaButton>
              
              <!-- Delete Button -->
              <IluriaButton
                variant="ghost"
                size="small"
                class="action-btn delete-individual-btn"
                @click.stop="deleteNotificationForUser(notification)"
                :disabled="notification.processing"
                :aria-label="$t('notifications.delete')"
              >
                <HugeiconsIcon :icon="Delete01Icon" :size="16" />
              </IluriaButton>
            </div>
            
            <div v-if="!notification.isRead" class="unread-indicator"></div>
            </div>
          </TransitionGroup>
        </div>

        <!-- Load More -->
        <div v-if="hasMoreNotifications" class="notification-footer">
          <IluriaButton
            variant="ghost"
            size="small"
            @click="loadMoreNotifications"
            :loading="loadingMore"
            class="load-more-btn"
          >
            {{ $t('notifications.loadMore') }}
          </IluriaButton>
        </div>
      </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, watch, getCurrentInstance } from 'vue'
import { useRouter } from 'vue-router'
import { useTheme } from '@/composables/useTheme'
import { useToast } from '@/services/toast.service'
import { useNotificationWebSocket } from '@/composables/useNotificationWebSocket'
import { useAuthStore } from '@/stores/auth.store'
import notificationService from '@/services/notification.service'
import { HugeiconsIcon } from '@hugeicons/vue'
import { ChevronDown, ChevronUp } from 'lucide-vue-next'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import {
  Notification01Icon,
  Settings02Icon,
  ShoppingBag01Icon,
  SecurityCheckIcon,
  SystemUpdateIcon,
  StarIcon,
  HelpCircleIcon,
  Mail01Icon,
  UserAdd01Icon,
  UserGroupIcon,
  Comment01Icon,
  ChartUpIcon,
  CheckmarkCircle01Icon,
  CheckmarkCircle02Icon,
  Time04Icon,
  UserGroup03Icon,
  AddTeamIcon,
  DollarSquareIcon,
  Chart02Icon,
  Megaphone02Icon,
  Delete01Icon,
  MailOpen01Icon,
  RowDeleteIcon
} from '@hugeicons-pro/core-stroke-rounded'

// Props
const props = defineProps({
  type: {
    type: String,
    default: 'user', // 'user' | 'store'
    validator: (value) => ['user', 'store'].includes(value)
  },
  storeId: {
    type: String,
    default: null
  }
})


// Composables
const router = useRouter()
const { themeMode } = useTheme()
const { showToast } = useToast()
const authStore = useAuthStore()
const instance = getCurrentInstance()
const $t = instance?.appContext.config.globalProperties.$t

// Generate unique instance ID to avoid state conflicts between different NotificationBell instances
const instanceId = `notification-bell-${props.type}-${props.storeId || 'global'}-${Math.random().toString(36).substr(2, 9)}`

// WebSocket para notificações em tempo real
const {
  isConnected: wsConnected,
  newNotification,
  startListening: startWebSocketListening,
  disconnect: disconnectWebSocket,
  markNotificationAsProcessed
} = useNotificationWebSocket()

// Instance-specific state (isolated per component instance)
const showNotifications = ref(false)
const notifications = ref([])
const unreadCount = ref(0)
const loading = ref(false)
const loadingMore = ref(false)
const markingAllAsRead = ref(false)
const bulkDeleting = ref(false)
const hasMoreNotifications = ref(false)
const hasNewNotifications = ref(false)
const currentPage = ref(1)
const pageSize = 10

// Protection against rapid clicks
const processingOperations = ref(new Set())

// Check if a notification is being processed
const isNotificationProcessing = (notificationId) => {
  return processingOperations.value.has(notificationId)
}

// Add notification to processing set
const startProcessing = (notificationId) => {
  processingOperations.value.add(notificationId)
}

// Remove notification from processing set
const stopProcessing = (notificationId) => {
  processingOperations.value.delete(notificationId)
}

// Local cache system removed - now using backend-only filtering

// Computed
const hasUnreadNotifications = computed(() => unreadCount.value > 0)

// Format badge count with protection against invalid values
const formatBadgeCount = (count) => {
  // Ensure count is a valid positive number
  const validCount = Math.max(0, parseInt(count) || 0)
  return validCount > 99 ? '99+' : validCount.toString()
}

// Notification icon mapping
const getNotificationIcon = (type) => {
  const iconMap = {
    // Segurança e Conta
    'SECURITY_ALERTS': SecurityCheckIcon,
    'ACCOUNT_CHANGES': UserAdd01Icon,
    'LOGIN_NOTIFICATIONS': SecurityCheckIcon,
    
    // Sistema e Atualizações
    'SYSTEM_UPDATES': SystemUpdateIcon,
    'MAINTENANCE_NOTIFICATIONS': SystemUpdateIcon,
    'FEATURE_ANNOUNCEMENTS': Megaphone02Icon,
    
    // Marketing e Email
    'PROMOTIONAL_EMAILS': Mail01Icon,
    'NEWSLETTER': Mail01Icon,
    'NEWSLETTER_SUBSCRIPTIONS': Mail01Icon,
    'PRODUCT_RECOMMENDATIONS': ChartUpIcon,
    
    // Interações Sociais
    'MENTIONS': Comment01Icon,
    'COMMENTS': Comment01Icon,
    'FOLLOW_NOTIFICATIONS': UserGroupIcon,
    
    // Relatórios e Analytics
    'WEEKLY_SUMMARY': Chart02Icon,
    'USAGE_REPORTS': ChartUpIcon,
    
    // Vendas e Comércio
    'NEW_SALES': DollarSquareIcon,
    'PRODUCT_REVIEWS': StarIcon,
    'PRODUCT_QUESTIONS': HelpCircleIcon,
    'NEW_CUSTOMER_REGISTRATIONS': UserAdd01Icon,
    
    // Colaboração e Equipe
    'COLLAB_INVITES': AddTeamIcon,
    'COLLAB_UPDATES': UserGroupIcon,
    'ROLE_CHANGES': UserAdd01Icon,
    
    // Notificações da Loja (Store-wide)
    'STORE_UPDATES': SystemUpdateIcon,
    'LOW_STOCK_ALERTS': Chart02Icon,
    'PAYMENT_NOTIFICATIONS': DollarSquareIcon,
    'ORDER_STATUS_CHANGES': ShoppingBag01Icon
  }
  return iconMap[type] || Notification01Icon
}

const getNotificationIconClass = (type) => {
  const classMap = {
    // Segurança e Conta - Vermelho/Laranja para alertas críticos
    'SECURITY_ALERTS': 'text-red-500',
    'ACCOUNT_CHANGES': 'text-orange-500',
    'LOGIN_NOTIFICATIONS': 'text-yellow-500',
    
    // Sistema e Atualizações - Azul para informações
    'SYSTEM_UPDATES': 'text-blue-500',
    'MAINTENANCE_NOTIFICATIONS': 'text-blue-600',
    'FEATURE_ANNOUNCEMENTS': 'text-indigo-500',
    
    // Marketing e Email - Azul claro e ciano
    'PROMOTIONAL_EMAILS': 'text-cyan-500',
    'NEWSLETTER': 'text-sky-500',
    'NEWSLETTER_SUBSCRIPTIONS': 'text-blue-400',
    'PRODUCT_RECOMMENDATIONS': 'text-purple-500',
    
    // Interações Sociais - Verde para interações positivas
    'MENTIONS': 'text-green-500',
    'COMMENTS': 'text-emerald-500',
    'FOLLOW_NOTIFICATIONS': 'text-teal-500',
    
    // Relatórios e Analytics - Roxo para dados
    'WEEKLY_SUMMARY': 'text-violet-500',
    'USAGE_REPORTS': 'text-purple-600',
    
    // Vendas e Comércio - Verde para dinheiro/vendas
    'NEW_SALES': 'text-green-600',
    'PRODUCT_REVIEWS': 'text-amber-500',
    'PRODUCT_QUESTIONS': 'text-blue-500',
    'NEW_CUSTOMER_REGISTRATIONS': 'text-emerald-500',
    
    // Colaboração e Equipe - Azul/roxo para equipe
    'COLLAB_INVITES': 'text-blue-600',
    'COLLAB_UPDATES': 'text-indigo-500',
    'ROLE_CHANGES': 'text-amber-600',
    
    // Notificações da Loja (Store-wide) - Cores específicas por criticidade
    'STORE_UPDATES': 'text-blue-500',
    'LOW_STOCK_ALERTS': 'text-red-500', // Critical - vermelho
    'PAYMENT_NOTIFICATIONS': 'text-green-600', // Financial - verde
    'ORDER_STATUS_CHANGES': 'text-purple-500'
  }
  return classMap[type] || 'text-gray-500'
}

// Badge count is now managed entirely by backend - no sync needed

// Methods
const toggleNotifications = () => {
  showNotifications.value = !showNotifications.value
  if (showNotifications.value && notifications.value.length === 0) {
    loadNotifications()
  }
}

const closeNotifications = () => {
  showNotifications.value = false
}

// Bulk delete function - direct delete without modal
const confirmBulkDelete = async () => {
  if (bulkDeleting.value || notifications.value.length === 0) return
  
  bulkDeleting.value = true
  try {
    // Store original count for debugging
    const originalUnreadCount = unreadCount.value
    const notificationsToDelete = [...notifications.value]
    
    // Delete all visible notifications sequentially to avoid database deadlocks
    const results = []
    for (let i = 0; i < notificationsToDelete.length; i++) {
      const notification = notificationsToDelete[i]
      try {
        const result = await notificationService.deleteNotificationForUser(notification.id)
        results.push(result)
        
        // Small delay to prevent overwhelming the database
        if (i < notificationsToDelete.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      } catch (error) {
        results.push({ success: false, error: error.message })
      }
    }
    
    // Clear local notifications array immediately
    notifications.value = []
    
    // Set badge to 0 since all visible notifications were deleted
    unreadCount.value = 0
    
    // Refresh from server to confirm accurate state
    await refreshUnreadCount()
    
    showToast($t('notifications.bulkDelete.success', 'All notifications deleted'), 'success')
    
  } catch (error) {
    showToast($t('notifications.bulkDelete.error'), 'error')
    // Reload notifications to get accurate state
    await loadNotifications()
  } finally {
    bulkDeleting.value = false
  }
}

const loadNotifications = async () => {
  loading.value = true
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(notificationType, storeId)
    
    // Reset current page when reloading notifications
    currentPage.value = 1
    
    // Backend provides consistent filtered results
    notifications.value = response.notifications || []
    hasMoreNotifications.value = response.hasMore || false
    
    // Ensure unread count is valid and specific to this instance
    const newCount = Math.max(0, parseInt(response.unreadCount) || 0)
    unreadCount.value = newCount
    
  } catch (error) {
    // Reset to safe state on error
    notifications.value = []
    unreadCount.value = 0
    hasMoreNotifications.value = false
    showToast($t('notifications.error.loadFailed'), 'error')
  } finally {
    loading.value = false
  }
}

const loadMoreNotifications = async () => {
  loadingMore.value = true
  try {
    currentPage.value++
    
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(
      notificationType, 
      storeId,
      currentPage.value,
      pageSize
    )
    
    // Backend already filters deleted notifications
    const newNotifications = response.notifications || []
    
    notifications.value = [...notifications.value, ...newNotifications]
    hasMoreNotifications.value = response.hasMore
    
  } catch (error) {
    currentPage.value--
    showToast($t('notifications.error.loadMoreFailed'), 'error')
  } finally {
    loadingMore.value = false
  }
}

const markAllAsRead = async () => {
  if (markingAllAsRead.value) return
  
  markingAllAsRead.value = true
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    // 1. FIRST call server and wait for response
    const response = await notificationService.markAllAsReadWithoutRemoving(notificationType, storeId)
    
    // 2. ONLY update local state after server confirms success
    if (response && (response.success || response.affectedCount >= 0)) {
      notifications.value.forEach((notification, index) => {
        if (!notification.isRead) {
          notification.isRead = true
        }
      })
      
      // Use server response count (backend is now consistent)
      const oldCount = unreadCount.value
      unreadCount.value = response.remainingCount !== undefined ? response.remainingCount : 0
      
    } else {
      // Server response indicates failure
      throw new Error('Server did not confirm success')
    }
    
  } catch (error) {
    // 3. Refresh from server to get accurate state (don't trust local state)
    await refreshUnreadCount()
    
    // 4. Optionally reload notifications to show correct read states
    if (showNotifications.value) {
      await loadNotifications()
    }
    
    showToast($t('notifications.error.markReadFailed'), 'error')
  } finally {
    markingAllAsRead.value = false
  }
}

const handleNotificationClick = async (notification) => {
  try {
    // 1. Mark notification as read when clicked (unless already read)
    if (!notification.isRead && !notification.processing) {
      await markNotificationAsRead(notification)
    }
    
    // 2. Navigate if actionUrl exists
    if (notification.actionUrl) {
      router.push(notification.actionUrl)
      closeNotifications()
    }
  } catch (error) {
    showToast($t('notifications.error.navigationFailed'), 'error')
  }
}

// NEW METHOD: Mark individual notification as read without removing
const markNotificationAsRead = async (notification) => {
  if (notification.processing || notification.isRead || isNotificationProcessing(notification.id)) return
  
  notification.processing = true
  startProcessing(notification.id)
  
  try {
    // Call server and wait for response
    const response = await notificationService.markNotificationAsReadWithoutRemoving(notification.id)
    
    // Update local state after server confirms success
    if (response && (response.success || response.affectedCount >= 0)) {
      notification.isRead = true
      
      // Update unread count
      if (response.remainingCount !== undefined) {
        unreadCount.value = Math.max(0, parseInt(response.remainingCount) || 0)
      } else {
        // Fallback: manually decrease count
        unreadCount.value = Math.max(0, unreadCount.value - 1)
      }
      
    } else {
      // Server response indicates failure
      throw new Error('Server did not confirm success')
    }
    
  } catch (error) {
    // Refresh accurate state from server (don't trust local state)
    await refreshUnreadCount()
    
    showToast($t('notifications.error.markReadFailed'), 'error')
  } finally {
    notification.processing = false
    stopProcessing(notification.id)
  }
}

// NEW METHOD: Delete notification for current user only
const deleteNotificationForUser = async (notification) => {
  if (notification.processing || isNotificationProcessing(notification.id)) return
  
  notification.processing = true
  startProcessing(notification.id)
  
  try {
    // Store the current isRead state to adjust count properly
    const wasUnread = !notification.isRead
    
    const response = await notificationService.deleteNotificationForUser(notification.id)
    
    // Server handles deletion, remove from local list immediately
    if (response && (response.success || response.affectedCount > 0)) {
      const index = notifications.value.findIndex(n => n.id === notification.id)
      if (index !== -1) {
        notifications.value.splice(index, 1)
      }
      
      // Update unread count properly
      if (response.remainingCount !== undefined) {
        unreadCount.value = Math.max(0, parseInt(response.remainingCount) || 0)
      } else {
        // Fallback: manually decrease count if it was unread
        if (wasUnread && unreadCount.value > 0) {
          unreadCount.value = Math.max(0, unreadCount.value - 1)
        }
        // Also refresh from server to be sure
        await refreshUnreadCount()
      }
      
    } else {
      // If server response indicates failure, show error
      showToast($t('notifications.error.deleteFailed'), 'error')
    }
  } catch (error) {
    // On error, refresh the entire list to get accurate state
    await loadNotifications()
    
    // Show specific error message based on the response
    const errorMessage = error.response?.data?.message || $t('notifications.error.deleteFailed')
    showToast(errorMessage, 'error')
  } finally {
    notification.processing = false
    stopProcessing(notification.id)
  }
}

const goToSettings = () => {
  // Different routes based on notification type
  if (props.type === 'store') {
    // For store notifications (NavBar), go to store email notifications
    const settingsRoute = `/settings/email-notifications`
    router.push(settingsRoute)
  } else {
    // For user notifications (UserNavBar), go to user settings notifications section
    const settingsRoute = `/user/settings?section=notifications`
    router.push(settingsRoute)
  }
  closeNotifications()
}

const formatTime = (timestamp) => {
  const now = new Date()
  const time = new Date(timestamp)
  const diffMs = now - time
  const diffMins = Math.floor(diffMs / 60000)
  const diffHours = Math.floor(diffMs / 3600000)
  const diffDays = Math.floor(diffMs / 86400000)

  if (diffMins < 1) return $t('notifications.time.now')
  if (diffMins < 60) return `${diffMins}${$t('notifications.time.minutes')}`
  if (diffHours < 24) return `${diffHours}${$t('notifications.time.hours')}`
  if (diffDays < 7) return `${diffDays}${$t('notifications.time.days')}`
  return time.toLocaleDateString()
}

// Handle new WebSocket notifications
const handleNewWebSocketNotification = (notification) => {
  // Only process notifications that are relevant to this instance
  const isRelevantForThisInstance = () => {
    if (props.type === 'user') {
      // User notifications: only process user-specific or global notifications
      return notification.user_id !== null || (notification.user_id === null && notification.store_id === null)
    } else if (props.type === 'store') {
      // Store notifications: only process store-specific notifications for this store
      return notification.store_id === props.storeId
    }
    return false
  }
  
  if (!isRelevantForThisInstance()) {
    return
  }
  
  // Adicionar à lista se não existir
  const existingIndex = notifications.value.findIndex(n => n.id === notification.id)
  
  if (existingIndex === -1) {
    notifications.value.unshift(notification)
    unreadCount.value++
    hasNewNotifications.value = true
    
    // Mostrar toast para notificação
    showToast(notification.title, 'info')
  }
  
  markNotificationAsProcessed()
}

// Start real-time updates
const startRealtimeUpdates = () => {
  // Conectar WebSocket apenas se estiver autenticado
  // O watch no useNotificationWebSocket já cuida da conexão automática
  if (authStore?.userLoggedIn) {
    startWebSocketListening()
  }
}

// Click outside handler
const handleClickOutside = (event) => {
  if (!event.target.closest('.notification-bell')) {
    closeNotifications()
  }
}

// Load initial unread count by getting actual notifications (more accurate)
const loadUnreadCount = async () => {
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(notificationType, storeId, 0, 100)
    
    // Backend provides consistent count - ensure it's valid
    const newCount = Math.max(0, parseInt(response.unreadCount) || 0)
    unreadCount.value = newCount

  } catch (error) {
    unreadCount.value = 0
  }
}

// Refresh unread count (useful for updates after operations)
const refreshUnreadCount = async () => {
  try {
    const notificationType = props.type
    const storeId = props.type === 'store' ? props.storeId : null
    
    const response = await notificationService.getUnreadNotifications(notificationType, storeId, 0, 100)
    
    // Backend provides consistent count - ensure it's valid
    const newCount = Math.max(0, parseInt(response.unreadCount) || 0)
    unreadCount.value = newCount
    
  } catch (error) {
    // Don't reset to 0 on error, keep current value to avoid confusion
  }
}

// Lifecycle
onMounted(async () => {
  document.addEventListener('click', handleClickOutside)
  
  // Load initial unread count immediately to show badge
  await loadUnreadCount()
  
  // Conectar WebSocket se já estiver autenticado
  startRealtimeUpdates()
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  disconnectWebSocket()
})

// Watch for new WebSocket notifications (ÚNICO listener)
watch(newNotification, (notification) => {
  if (notification) {
    handleNewWebSocketNotification(notification)
  }
})

// Watch for new notifications animation
watch(hasNewNotifications, (newVal) => {
  if (newVal) {
    setTimeout(() => {
      hasNewNotifications.value = false
    }, 3000) // Stop pulsing after 3 seconds
  }
})

// Watch for prop changes to reload count
watch(() => [props.type, props.storeId], async ([newType, newStoreId], [oldType, oldStoreId]) => {
  // Reset state when context changes
  notifications.value = []
  unreadCount.value = 0
  hasMoreNotifications.value = false
  currentPage.value = 1
  
  // Load count for new context
  await loadUnreadCount()
}, { immediate: false })
</script>

<style scoped>
.notification-bell {
  position: relative;
  display: flex;
  align-items: center;
}

/* Navbar button styles to ensure functionality */
.navbar-button {
  background: transparent;
  border: none;
  padding: 8px;
  border-radius: 6px;
  color: var(--iluria-color-navbar-fg);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
}

.navbar-button:hover {
  background: transparent !important;
  transform: translateY(-1px);
}

.navbar-button:active {
  transform: translateY(0) scale(0.98);
}

.navbar-button.nav-button-active {
  background: var(--iluria-color-navbar-active, rgba(0, 0, 0, 0.1));
  color: var(--iluria-color-primary, #3b82f6);
}

.has-notifications {
  color: var(--iluria-color-primary, #3b82f6);
}

.notification-badge {
  position: absolute;
  top: -6px;
  right: -6px;
  background: var(--iluria-color-primary, #3b82f6);
  color: var(--iluria-color-secondary, #000000);
  font-size: 11px;
  font-weight: 700;
  min-width: 20px;
  height: 20px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  padding: 0 6px;
  border: none;
  box-sizing: border-box;
  white-space: nowrap;
  font-feature-settings: "tnum";
  font-variant-numeric: tabular-nums;
  text-align: center;
  box-shadow: none;
  text-shadow: none;
}

.notification-badge.pulse {
  animation: pulse 2s infinite;
}


/* Tema bumblebee - contorno preto no ícone do sino para visibilidade */
.theme-bumblebee .navbar-button .icon-desktop {
  color: #000000 !important;
}

.theme-bumblebee .navbar-button:hover .icon-desktop {
  color: inherit;
}

.chevron-icon {
  color: currentColor;
  transition: all 0.2s ease;
  margin-left: 4px;
}

/* Enhanced Badge scale animation with bounce effect */
.badge-scale-enter-active {
  transition: all 0.5s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.badge-scale-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.badge-scale-enter-from {
  opacity: 0;
  transform: scale(0.3) rotate(-10deg);
}

.badge-scale-leave-to {
  opacity: 0;
  transform: scale(1.3) rotate(5deg);
}

.badge-scale-enter-to {
  opacity: 1;
  transform: scale(1) rotate(0deg);
}

/* Chevron rotation animation */
.chevron-rotate-enter-active,
.chevron-rotate-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.chevron-rotate-enter-from {
  opacity: 0;
  transform: rotate(-90deg) scale(0.8);
}

.chevron-rotate-leave-to {
  opacity: 0;
  transform: rotate(90deg) scale(0.8);
}

.notification-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 400px;
  max-width: 90vw;
  background: var(--iluria-color-container-bg, #f9fafb);
  border: 1px solid var(--iluria-color-border, #e5e7eb);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1)), 
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1001;
  overflow: hidden;
  max-height: 500px;
  display: flex;
  flex-direction: column;
  backdrop-filter: blur(8px);
  transform-origin: top right;
  pointer-events: auto;
}

/* Enhanced Dropdown slide animation */
.dropdown-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dropdown-slide-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.dropdown-slide-enter-from {
  opacity: 0;
  transform: translateY(-20px) scale(0.9) rotateX(-10deg);
  filter: blur(4px);
}

.dropdown-slide-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95) rotateX(5deg);
  filter: blur(2px);
}

.dropdown-slide-enter-to {
  opacity: 1;
  transform: translateY(0) scale(1) rotateX(0deg);
  filter: blur(0px);
}

/* Enhanced Notification Item Animation */
.notification-item-enter-active {
  transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  transition-delay: var(--delay, 0ms);
}

.notification-item-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.notification-item-move {
  transition: transform 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.notification-item-enter-from {
  opacity: 0;
  transform: translateX(-30px) scale(0.9);
  filter: blur(2px);
}

.notification-item-leave-to {
  opacity: 0;
  transform: translateX(30px) scale(0.95);
  filter: blur(1px);
}

.notification-item-enter-to {
  opacity: 1;
  transform: translateX(0) scale(1);
  filter: blur(0px);
}

.notification-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-surface);
  pointer-events: auto;
  z-index: 1;
  position: relative;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.notification-actions {
  display: flex;
  gap: 6px;
  align-items: center;
  flex-wrap: wrap;
}

.action-button {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 6px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 1px solid var(--iluria-color-border, #e5e7eb);
  background: var(--iluria-color-container-bg, #f9fafb);
}

.action-button:hover {
  transform: translateY(-1px);
  border-color: var(--iluria-color-border-hover, #d1d5db);
}

.action-button:disabled {
  opacity: 0.5 !important;
  cursor: not-allowed !important;
  transform: none !important;
}

.mark-read-btn {
  color: var(--iluria-color-success, #10b981);
  border-color: var(--iluria-color-success-light, rgba(16, 185, 129, 0.2));
}

.mark-read-btn:hover:not(:disabled) {
  background: var(--iluria-color-success-light, rgba(16, 185, 129, 0.1));
}

.bulk-delete-btn {
  color: var(--iluria-color-danger, #dc2626);
  border-color: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.2));
}

.bulk-delete-btn:hover:not(:disabled) {
  background: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.1));
}

.settings-btn {
  color: var(--iluria-color-text-secondary, #6b7280);
}

.settings-btn:hover:not(:disabled) {
  color: var(--iluria-color-text-primary, #1f2937);
  background: var(--iluria-color-hover, rgba(0, 0, 0, 0.05));
}

.notification-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  pointer-events: auto;
}

.notification-loading,
.notification-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

.empty-icon {
  color: var(--iluria-color-text-secondary);
  margin-bottom: 16px;
}

.notification-list {
  flex: 1;
  overflow-y: auto;
  max-height: 360px;
  pointer-events: auto;
  -webkit-overflow-scrolling: touch;
  overscroll-behavior: contain;
  scroll-behavior: smooth;
}

.notification-item {
  display: flex;
  padding: 16px 20px;
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: pointer;
  transition: background-color 0.2s ease;
  position: relative;
  align-items: flex-start;
  pointer-events: auto;
  user-select: none;
}

.notification-item:hover {
  background: var(--iluria-color-surface);
}

.notification-item:last-child {
  border-bottom: none;
}

.notification-item.unread {
  background: var(--iluria-color-primary-light);
}

.notification-item.critical {
  border-left: 3px solid var(--iluria-color-danger);
}

.notification-icon {
  margin-right: 12px;
  flex-shrink: 0;
  padding-top: 2px;
}

.notification-body {
  flex: 1;
  min-width: 0;
}

.notification-subject {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.notification-message {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 6px 0;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.notification-time {
  font-size: 11px;
  color: var(--iluria-color-text-tertiary);
}

.notification-actions-individual {
  display: flex;
  flex-direction: row;
  gap: 4px;
  margin-left: 8px;
  flex-shrink: 0;
  align-items: center;
}

/* Always show on mobile, hover on desktop */
@media (max-width: 768px) {
  .notification-actions-individual {
    opacity: 1;
  }
}

@media (min-width: 769px) {
  .notification-actions-individual {
    opacity: 0;
    transition: opacity 0.2s ease;
  }
  
  .notification-item:hover .notification-actions-individual {
    opacity: 1;
  }
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 32px;
  height: 32px;
  padding: 6px;
  border-radius: 6px;
  border: 1px solid transparent;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.mark-read-individual-btn {
  color: var(--iluria-color-success, #10b981);
}

.mark-read-individual-btn:hover:not(:disabled) {
  background: var(--iluria-color-success-light, rgba(16, 185, 129, 0.1));
  border-color: var(--iluria-color-success-light, rgba(16, 185, 129, 0.2));
}

.delete-individual-btn {
  color: var(--iluria-color-danger, #dc2626);
}

.delete-individual-btn:hover:not(:disabled) {
  background: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.1));
  border-color: var(--iluria-color-danger-light, rgba(220, 38, 38, 0.2));
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.unread-indicator {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  width: 8px;
  height: 8px;
  background: var(--iluria-color-primary);
  border-radius: 50%;
  flex-shrink: 0;
}

.notification-footer {
  padding: 12px 20px;
  border-top: 1px solid var(--iluria-color-border);
  text-align: center;
}

.load-more-btn {
  width: 100%;
}

.notification-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background: transparent;
  pointer-events: auto;
}

@media (max-width: 768px) {
  .notification-overlay {
    background: rgba(0, 0, 0, 0.3);
    z-index: 999;
  }
}

/* Animations */
@keyframes subtle-bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3px);
  }
  60% {
    transform: translateY(-1px);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0.8);
  }
  25% {
    transform: scale(1.1) rotate(1deg);
    opacity: 0.9;
    box-shadow: 0 0 0 5px rgba(59, 130, 246, 0.4);
  }
  50% {
    transform: scale(1.15) rotate(0deg);
    opacity: 0.8;
    box-shadow: 0 0 0 12px rgba(59, 130, 246, 0.1);
  }
  75% {
    transform: scale(1.05) rotate(-1deg);
    opacity: 0.9;
    box-shadow: 0 0 0 18px rgba(59, 130, 246, 0);
  }
  100% {
    transform: scale(1) rotate(0deg);
    opacity: 1;
    box-shadow: 0 0 0 0 rgba(59, 130, 246, 0);
  }
}

/* Dropdown slide animation - enhanced */
.dropdown-slide-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dropdown-slide-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.dropdown-slide-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-slide-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.98);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}


/* Enhanced Mobile Responsiveness */
@media (max-width: 768px) {
  .notification-dropdown {
    position: fixed;
    top: 70px;
    right: 10px;
    left: 10px;
    width: auto;
    max-width: none;
    max-height: calc(100vh - 90px);
    transform-origin: top center;
    z-index: 1002;
    pointer-events: auto;
  }
  
  .notification-toggle-button {
    padding: 8px 10px;
  }
  
  .notification-icon {
    width: 24px;
    height: 24px;
  }
  
  .notification-icon svg {
    width: 20px;
    height: 20px;
  }
  
  .notification-badge {
    min-width: 18px;
    height: 18px;
    font-size: 10px;
    top: -4px;
    right: -4px;
  }
  
  .notification-item {
    padding: 14px 12px;
  }
  
  .notification-header {
    padding: 14px 12px;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
  }
  
  .notification-actions {
    gap: 6px;
    flex-wrap: nowrap;
    justify-content: flex-end;
  }
  
  .action-button {
    min-width: 36px;
    height: 36px;
    padding: 6px;
  }
  
  .action-btn {
    min-width: 36px;
    height: 36px;
    padding: 6px;
  }
  
  .notification-actions-individual {
    gap: 6px;
    margin-left: 8px;
  }
  
  .notification-list {
    max-height: calc(65vh - 80px);
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  .notification-title {
    font-size: 15px;
  }
}

@media (max-width: 480px) {
  .notification-dropdown {
    top: 65px;
    right: 8px;
    left: 8px;
    max-height: calc(100vh - 80px);
  }
  
  .notification-header {
    padding: 12px 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .notification-actions {
    align-self: stretch;
    justify-content: space-between;
    gap: 4px;
    flex-wrap: wrap;
  }
  
  .notification-list {
    max-height: calc(60vh - 100px);
    -webkit-overflow-scrolling: touch;
    overscroll-behavior: contain;
  }
  
  .notification-item {
    padding: 12px 10px;
  }
  
  .notification-actions-individual {
    margin-left: 6px;
    gap: 4px;
  }
  
  .action-button {
    min-width: 34px;
    height: 34px;
    padding: 6px;
  }
  
  .action-btn {
    min-width: 34px;
    height: 34px;
    padding: 6px;
  }
}
</style>