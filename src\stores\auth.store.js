import { defineStore } from 'pinia';
import router from '@/views/index/router';
import { ref, computed } from 'vue';
import authService from '@/services/auth.service';
import tokenManager from '@/services/tokenManager.service';
import userSessionService from '@/services/userSession.service';

export const useAuthStore = defineStore('auth', () => {
    const userLoggedIn = ref(false);
    const userName = ref(undefined);
    const userToken = ref(undefined);
    const refreshToken = ref(undefined); // JWT refresh token for token renewal
    const storeToken = ref(undefined);
    const jwtToken = ref(undefined); // Legacy - will be phased out
    const userEmail = ref(undefined); 
    const rememberMe = ref(false);
    const mfaRequired = ref(false);
    const passwordRecoveryStatus = ref(null);
    const loading = ref(false);

    // Profile data state
    const userFirstName = ref(undefined);
    const userLastName = ref(undefined);
    const userProfilePictureUrl = ref(undefined);

    // Session management state
    const currentSessionId = ref(undefined);
    const currentDeviceFingerprint = ref(undefined);
    const sessionValidationActive = ref(false);
    
    // Critical: State transition locks to prevent race conditions
    const tokenTransitionLock = ref(false);
    
    // Memory leak prevention: Track intervals for proper cleanup
    const activeIntervals = new Set();

    async function login(username, password, remember) {
        try {
            authService.setCredentials(username, password, remember);
            const response = await authService.login();

            if (response.mfaEnabled === "false") {
                userLoggedIn.value = true;
                userName.value = response.userName;
                userEmail.value = response.userEmail;
                userToken.value = response.jwt;
                refreshToken.value = response.refreshToken; // Store refresh token
                jwtToken.value = response.jwt; // Keep for compatibility
                
                // Extract session information from token
                extractAndSetSessionInfo(response.jwt);
                
                // Set user token for user operations (store listing)
                setUserTokenHeaders();
                
                // Inicia monitoramento de sessão
                startSessionMonitoring();

                // Carrega dados do perfil do usuário
                loadUserProfile();
            } else {
                mfaRequired.value = true;
            }

            rememberMe.value = remember;
            return response;
        } catch (error) {
            userLoggedIn.value = false;
            userName.value = undefined;
            userEmail.value = undefined;
            userToken.value = undefined;
            refreshToken.value = undefined;
            jwtToken.value = undefined;
            rememberMe.value = false;
            mfaRequired.value = false;
            throw error;
        }
    }

    async function verifyMfaCode(code, email) {
        try {
            const response = await authService.verifyMfaCode(code, email);

            if (response.jwt) {
                userToken.value = response.jwt;
                refreshToken.value = response.refreshToken; // Store refresh token from MFA
                jwtToken.value = response.jwt; // Keep for compatibility
                userLoggedIn.value = true;
                userName.value = response.userName;
                userEmail.value = response.userEmail;
                mfaRequired.value = false;
                
                // Extract session information from token
                extractAndSetSessionInfo(response.jwt);
                
                // Set user token for user operations
                setUserTokenHeaders();
                
                // Inicia monitoramento de sessão
                startSessionMonitoring();

                // Carrega dados do perfil do usuário
                loadUserProfile();
                
                return response;
            } else {
                throw new Error('Código MFA inválido');
            }
        } catch (error) {
            throw error;
        }
    }

    async function forgotPassword(email) {
        try {
            loading.value = true;
            const response = await authService.requestPasswordRecovery(email);
    
            if (response.success) {
                return { success: true, message: "E-mail de recuperação enviado com sucesso." };
            } else {
                throw new Error('Erro ao solicitar recuperação de senha');
            }
        } catch (error) {
            return { success: false, message: 'Falha ao tentar enviar o e-mail de recuperação.' };
        } finally {
            loading.value = false;
        }
    }
    
    async function RequestedchangePassword(recoveryId, email, newPassword) {
        try {
            loading.value = true;
            const response = await authService.RequestedchangePassword(recoveryId, email, newPassword);
            
            if (response.success) {
                return { success: true, message: 'Senha alterada com sucesso!' };
            } else {
                throw new Error('Erro ao alterar a senha');
            }
        } catch (error) {
            return { success: false, message: 'Falha ao tentar alterar a senha.' };
        } finally {
            loading.value = false;
        }
    }

    async function changePassword(currentPassword, newPassword, confirmNewPassword) {
        if (newPassword !== confirmNewPassword) {
            throw new Error('As senhas novas não coincidem');
        }
    
        try {
            loading.value = true;
            const requestBody = {
                email: userEmail.value,
                currentPassword,
                newPassword
            };
    
            const response = await authService.changePassword(requestBody);
    
            if (response.success) {
                return { success: true, message: 'Senha alterada com sucesso!' };
            } else {
                throw new Error(response.message || 'Erro ao alterar a senha');
            }
        } catch (error) {
            return { success: false, message: error.message, code: error.code };
        } finally {
            loading.value = false;
        }
    }
    
    async function signupRequest(email) {
        try {
            loading.value = true;
            const response = await authService.signupRequest(email);
            
            if (response.success) {
                return { success: true, message: 'E-mail de confirmação enviado!' };
            } else {
                throw new Error(response.message || 'Erro ao solicitar cadastro');
            }
        } catch (error) {
            return { success: false, message: error.message };
        } finally {
            loading.value = false;
        }
    }
    
    async function verifySignup(signupId, email, password, firstName, lastName) {
        try {
            loading.value = true;
            const response = await authService.verifySignup(signupId, email, password, firstName, lastName);
    
            if (response.success) {
                return response;
            } else {
                throw new Error('Erro ao verificar o link de cadastro');
            }
        } catch (error) {
            throw error;
        } finally {
            loading.value = false;
        }
    }

    async function logout() {
        // NOVO: Invalidar sessão no servidor primeiro
        try {
            await authService.logout();
        } catch (error) {
            console.warn('Erro ao invalidar sessão no servidor, continuando com logout local:', error);
        }
        
        // MEMORY LEAK FIX: Clear all intervals before logout
        stopSessionMonitoring();
        stopSessionValidation();
        
        // Clear all session timeouts before logout
        tokenManager.cleanup();
        
        // Clear session state
        clearSessionState();
        
        userLoggedIn.value = false;
        userName.value = undefined;
        userToken.value = undefined;
        refreshToken.value = undefined;
        storeToken.value = undefined;
        jwtToken.value = undefined;
        rememberMe.value = false;
        mfaRequired.value = false;

        // Clear store selection
        try {
            const { useStoreStore } = await import('@/stores/store.store');
            const storeStore = useStoreStore();
            storeStore.clearStore();
        } catch (error) {
            console.error('Error clearing store:', error);
        }

        router.push({ name: 'Login' });
    }

    function setTokenData(authData) {
        jwtToken.value = authData.token || authData.jwt;
        userName.value = authData.userName;
        userEmail.value = authData.userEmail;
        userLoggedIn.value = true;
        
        if (typeof window !== 'undefined' && window.axios) {
            window.axios.defaults.headers.common['Authorization'] = `Bearer ${jwtToken.value}`;
        }
    }

    async function setStoreToken(token) {
        // CRITICAL FIX: Implement proper lock for token transitions
        while (tokenTransitionLock.value) {
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        tokenTransitionLock.value = true;
        
        try {
            // Limpa token anterior primeiro para evitar race conditions
            if (storeToken.value && storeToken.value !== token) {
                await clearStoreToken();
            }
            
            storeToken.value = token;
            setStoreTokenHeaders();
        
            // Set up session timeout for store token
            if (token) {
                const storeInfo = tokenManager.extractUserInfo(token);
                const storeId = storeInfo?.storeId || 'unknown';
                
                tokenManager.setSessionTimeout(`store_${storeId}`, () => {
                    clearStoreToken();
                });
            }
        } finally {
            tokenTransitionLock.value = false; // Always release lock
        }
    }

    async function clearStoreToken() {
        if (storeToken.value) {
            const storeInfo = tokenManager.extractUserInfo(storeToken.value);
            const storeId = storeInfo?.storeId || 'unknown';
            
            // Clear the session timeout
            tokenManager.clearSessionTimeout(`store_${storeId}`);
            
        }
        
        storeToken.value = undefined;
        
        // Clear store token headers
        if (typeof window !== 'undefined' && window.axios) {
            delete window.axios.defaults.headers.common['X-Store-ID'];
            // Também limpa o Authorization header
            delete window.axios.defaults.headers.common['Authorization'];
        }
    }

    function setUserTokenHeaders() {
        if (typeof window !== 'undefined' && window.axios && userToken.value) {
            window.axios.defaults.headers.common['Authorization'] = `Bearer ${userToken.value}`;
        }
    }

    function setStoreTokenHeaders() {
        if (typeof window !== 'undefined' && window.axios && storeToken.value) {
            window.axios.defaults.headers.common['Authorization'] = `Bearer ${storeToken.value}`;
        }
    }

    function getCurrentToken() {
        // Return store token if available (for store operations), otherwise user token
        return storeToken.value || userToken.value;
    }

    function validateTokens() {
        const tokens = {
            userToken: userToken.value,
            storeToken: storeToken.value
        };
        
        const validations = {};
        
        for (const [tokenType, token] of Object.entries(tokens)) {
            if (token) {
                validations[tokenType] = tokenManager.validateToken(token);
            }
        }
        
        return validations;
    }

    function getTokenInfo(tokenType = 'current') {
        let token = null;
        
        if (tokenType === 'user') {
            token = userToken.value;
        } else if (tokenType === 'store') {
            token = storeToken.value;
        } else {
            token = getCurrentToken();
        }
        
        if (!token) return null;
        
        return tokenManager.extractUserInfo(token);
    }

    /**
     * Invalida uma sessão específica (chamado quando o usuário termina uma sessão remotamente)
     */
    async function invalidateSession(sessionId) {
        try {
            await userSessionService.terminateSession(sessionId);
        
        } catch (error) {
            console.error('Erro ao invalidar sessão:', error);
            throw error;
        }
    }

    /**
     * Verifica se a sessão atual foi invalidada remotamente
     */
    function checkSessionValidity() {
        // Esta função pode ser chamada periodicamente para verificar
        // se a sessão atual ainda é válida no servidor
        return userSessionService.updateSessionActivity();
    }

    /**
     * Registra atividade da sessão atual
     */
    function updateSessionActivity() {
        if (userLoggedIn.value && userToken.value) {
            userSessionService.updateSessionActivity().catch(error => {
                console.warn('Erro ao atualizar atividade da sessão:', error);
            });
        }
    }

    /**
     * Configura monitoramento automático de atividade
     */
    function startSessionMonitoring() {
        // Clear any existing session monitoring first
        stopSessionMonitoring();
        
        // Atualiza atividade a cada 5 minutos
        const interval = setInterval(() => {
            if (userLoggedIn.value) {
                updateSessionActivity();
            } else {
                clearInterval(interval);
                activeIntervals.delete(interval);
            }
        }, 5 * 60 * 1000); // 5 minutos

        // MEMORY LEAK FIX: Track interval for cleanup
        activeIntervals.add(interval);
        return interval;
    }

    /**
     * Para monitoramento de sessão e limpa intervals
     */
    function stopSessionMonitoring() {
        activeIntervals.forEach(interval => {
            clearInterval(interval);
        });
        activeIntervals.clear();
    }

    /**
     * Extrai e define informações de sessão do token
     */
    function extractAndSetSessionInfo(token) {
        try {
            const sessionInfo = tokenManager.extractSessionInfo(token);
            if (sessionInfo) {
                currentSessionId.value = sessionInfo.sessionId;
                currentDeviceFingerprint.value = sessionInfo.deviceFingerprint;
            }
            
            // Se não tem sessionId no token, gera fingerprint localmente
            if (!currentDeviceFingerprint.value) {
                currentDeviceFingerprint.value = userSessionService.getCurrentDeviceFingerprint();
            }
        } catch (error) {
            console.warn('Erro ao extrair informações de sessão do token:', error);
            // Fallback: gera fingerprint localmente
            currentDeviceFingerprint.value = userSessionService.getCurrentDeviceFingerprint();
        }
    }

    /**
     * Limpa estado de sessão
     */
    function clearSessionState() {
        currentSessionId.value = undefined;
        currentDeviceFingerprint.value = undefined;
        sessionValidationActive.value = false;
    }

    /**
     * Obtém informações da sessão atual
     */
    function getCurrentSessionInfo() {
        return {
            sessionId: currentSessionId.value,
            deviceFingerprint: currentDeviceFingerprint.value,
            isLoggedIn: userLoggedIn.value,
            userEmail: userEmail.value,
            userName: userName.value
        };
    }

    /**
     * Verifica se a sessão atual foi invalidada remotamente
     */
    async function checkSessionValidity() {
        if (!userLoggedIn.value || !userToken.value) {
            return true; // Se não está logado, considera válido
        }
        
        try {
            const result = await userSessionService.updateSessionActivity();
            
            if (!result.sessionValid) {
                console.warn('Sessão foi invalidada remotamente');
                logout();
                return false;
            }
            
            return true;
        } catch (error) {
            console.error('Erro ao verificar validade da sessão:', error);
            
            // Se erro 401, sessão foi invalidada
            if (error.response?.status === 401) {
                logout();
                return false;
            }
            
            // Para outros erros, assume que sessão ainda é válida
            return true;
        }
    }

    /**
     * Inicia validação periódica de sessão
     */
    function startSessionValidation() {
        if (sessionValidationActive.value) {
            return; // Já está ativo
        }
        
        sessionValidationActive.value = true;
        
        const validationInterval = setInterval(async () => {
            if (!userLoggedIn.value) {
                clearInterval(validationInterval);
                activeIntervals.delete(validationInterval);
                sessionValidationActive.value = false;
                return;
            }
            
            const isValid = await checkSessionValidity();
            if (!isValid) {
                clearInterval(validationInterval);
                activeIntervals.delete(validationInterval);
                sessionValidationActive.value = false;
            }
        }, 60000); // Verifica a cada 1 minuto
        
        // MEMORY LEAK FIX: Track interval for cleanup
        activeIntervals.add(validationInterval);
        return validationInterval;
    }

    /**
     * Para validação periódica de sessão
     */
    function stopSessionValidation() {
        sessionValidationActive.value = false;
        // Intervals are cleaned up by the general cleanup
    }

    /**
     * Força atualização do token de usuário (usado pelo TokenManager)
     */
    function setUserToken(newToken) {
        userToken.value = newToken;
        jwtToken.value = newToken; // Keep for compatibility
        extractAndSetSessionInfo(newToken);
        setUserTokenHeaders();
    }

    /**
     * Atualiza tanto o access token quanto o refresh token
     */
    function updateTokens(newAccessToken, newRefreshToken) {
        userToken.value = newAccessToken;
        refreshToken.value = newRefreshToken;
        jwtToken.value = newAccessToken; // Keep for compatibility
        extractAndSetSessionInfo(newAccessToken);
        setUserTokenHeaders();
    }

    /**
     * Obtém o refresh token atual
     */
    function getRefreshToken() {
        return refreshToken.value;
    }

    /**
     * Atualiza dados do perfil do usuário
     */
    function updateUserProfile(profileData) {
        if (profileData.firstName !== undefined) {
            userFirstName.value = profileData.firstName;
        }
        if (profileData.lastName !== undefined) {
            userLastName.value = profileData.lastName;
        }
        if (profileData.profilePictureUrl !== undefined) {
            userProfilePictureUrl.value = profileData.profilePictureUrl;
        }

        // Atualiza o userName combinando firstName e lastName
        if (userFirstName.value || userLastName.value) {
            userName.value = `${userFirstName.value || ''} ${userLastName.value || ''}`.trim();
        }
    }

    /**
     * Carrega dados do perfil do usuário
     */
    async function loadUserProfile() {
        try {
            const { default: userProfileService } = await import('@/services/userProfile.service');
            const profileData = await userProfileService.getUserProfileSettings();

            if (profileData) {
                updateUserProfile(profileData);
            }
        } catch (error) {
            console.warn('Erro ao carregar perfil do usuário:', error);
            // Não é um erro crítico, continua sem o perfil
        }
    }

    /**
     * Computed para dados completos do usuário
     */
    const user = computed(() => ({
        name: userName.value,
        email: userEmail.value,
        firstName: userFirstName.value,
        lastName: userLastName.value,
        profilePictureUrl: userProfilePictureUrl.value
    }));

    return {
        // Auth state
        userLoggedIn,
        userName,
        userEmail,
        userToken,
        refreshToken,
        storeToken,
        jwtToken,
        rememberMe,
        mfaRequired,
        passwordRecoveryStatus,
        loading,

        // Profile state
        userFirstName,
        userLastName,
        userProfilePictureUrl,
        user,

        // Session state
        currentSessionId,
        currentDeviceFingerprint,
        sessionValidationActive,
        tokenTransitionLock,
        
        // Auth methods
        verifySignup, 
        login,
        logout,
        verifyMfaCode,
        forgotPassword,
        RequestedchangePassword,
        changePassword,
        signupRequest,
        setTokenData,
        setUserToken,
        updateTokens,
        getRefreshToken,
        updateUserProfile,
        loadUserProfile,
        
        // Token management
        setStoreToken,
        clearStoreToken,
        setUserTokenHeaders,
        setStoreTokenHeaders,
        getCurrentToken,
        validateTokens,
        getTokenInfo,
        
        // Session management
        invalidateSession,
        checkSessionValidity,
        updateSessionActivity,
        startSessionMonitoring,
        stopSessionMonitoring,
        startSessionValidation,
        stopSessionValidation,
        getCurrentSessionInfo,
        extractAndSetSessionInfo,
        clearSessionState
    };
}, {
    persist: true
});
