// Import all English translations
import enGeneral from './en/general_en.json';
import enErrors from './en/errors_en.json';
import enContentEditor from './en/contentEditor_en.json';
import enCategory from './en/category_en.json';
import enLogin from './en/login_en.json';
import enMedia from './en/media_en.json';
import enOrder from './en/order_en.json';
import enPages from './en/pages_en.json';
import enProduct from './en/product_en.json';
import enSeo from './en/seo_en.json';
import enSettings from './en/settings_en.json';
import enValidation from './en/validation_en.json';
import enFileManager from './en/fileManager_en.json';
import enOriginCep from './en/originCep_en.json';
import enCustomerInfo from './en/customerInfo_en.json';
import enAddressList from './en/addressList_en.json';
import enNotifications from './en/notifications_en.json';
import enEmailNotifications from './en/emailNotifications_en.json';
import enSeoConfig from './en/seoConfig_en.json';
import enCouponManager from './en/couponManager_en.json';
import enCouponForm from './en/couponForm_en.json';
import enStoreSeo from './en/storeSeo_en.json';
import enUrlRedirect from './en/urlRedirect_en.json';
import enSocialMedia from './en/socialMedia_en.json';
import enSocialLink from './en/socialLink_en.json';
import enDomainUrl from './en/domainUrl_en.json';
import enEditCategory from './en/editCategory_en.json';
import enPromotions from './en/promotions_en.json';
import enPagination from './en/pagination_en.json';
import enShippingConfig from './en/shippingConfig_en.json';
import enToast from './en/toast_en.json';
import enCustomer from './en/customer_en.json';
import enLayoutEditor from './en/layoutEditor_en.json';
import enCollectionProduct from './en/collectionProduct_en.json';
import enConfirmDialog from './en/confirmDialog_en.json';
import enProductVariations from './en/productVariations_en.json';
import enStatementLayoutComponent from './en/statementLayoutComponent_en.json';
import enCombinedProduct from './en/combinedProduct_en.json';
import enDashboard from './en/dashboard_en.json';
import enMinimumOrder from './en/minimumOrder_en.json';
import enFooter from './en/footer_en.json';
import enSeoProductVariation from './en/seoProductVariation_en.json';
import enThemeSelector from './en/themeSelector_en.json';
import enThemes from './en/themes_en.json';
import enMeasurementTable from './en/measurementTable_en.json';
import enQuestions from './en/questions_en.json';
import enDomain from './en/domain_en.json';
import enCoupons from './en/coupons_en.json';
import enStoreBrandAssets from './en/storeBrandAssets_en.json';
import enStorePhysicalData from './en/storePhysicalData_en.json';
import enCommunity from './en/community_en.json';
import enBlogPost from './en/blogPost_en.json';
import enBlogCategory from './en/blogCategory_en.json';
import enBlogDashboard from './en/blogDashboard_en.json';
import enUserMenu from './en/userMenu_en.json';
import enUserSettings from './en/userSettings_en.json';
import enPaymentMethods from './en/paymentMethods_en.json';
import enSignup from './en/signup_en.json';
import enPasswordRecovery from './en/passwordRecovery_en.json';
import enStores from './en/stores_en.json';
import enScreenSearch from './en/screenSearch_en.json';
import enStoreMode from './en/storeMode_en.json';
import enUserProfile from './en/userProfile_en.json';
import enPublishing from './en/publishing_en.json';

// Import all Portuguese translations
import ptGeneral from './pt-br/general_pt.json';
import ptErrors from './pt-br/errors_pt.json';
import ptContentEditor from './pt-br/contentEditor_pt.json';
import ptCategory from './pt-br/category_pt.json';
import ptLogin from './pt-br/login_pt.json';
import ptMedia from './pt-br/media_pt.json';
import ptOrder from './pt-br/order_pt.json';
import ptPages from './pt-br/pages_pt.json';
import ptProduct from './pt-br/product_pt.json';
import ptSeo from './pt-br/seo_pt.json';
import ptSettings from './pt-br/settings_pt.json';
import ptValidation from './pt-br/validation_pt.json';
import ptFileManager from './pt-br/fileManager_pt.json';
import ptOriginCep from './pt-br/originCep_pt.json';
import ptCustomerInfo from './pt-br/customerInfo_pt.json';
import ptAddressList from './pt-br/addressList_pt.json';
import ptNotifications from './pt-br/notifications_pt.json';
import ptEmailNotifications from './pt-br/emailNotifications_pt.json';
import ptSeoConfig from './pt-br/seoConfig_pt.json';
import ptCouponManager from './pt-br/couponManager_pt.json';
import ptCouponForm from './pt-br/couponForm_pt.json';
import ptPagination from './pt-br/pagination_pt.json';
import ptStoreSeo from './pt-br/storeSeo_pt.json';
import ptUrlRedirect from './pt-br/urlRedirect_pt.json';
import ptSocialMedia from './pt-br/socialMedia_pt.json';
import ptSocialLink from './pt-br/socialLink_pt.json';
import ptDomainUrl from './pt-br/domainUrl_pt.json';
import ptEditCategory from './pt-br/editCategory_pt.json';
import ptPromotions from './pt-br/promotions_pt.json';
import ptShippingConfig from './pt-br/shippingConfig_pt.json';
import ptToast from './pt-br/toast_pt.json';
import ptCustomer from './pt-br/customer_pt.json';
import ptLayoutEditor from './pt-br/layoutEditor_pt.json';
import ptCollectionProduct from './pt-br/collectionProduct_pt.json';
import ptConfirmDialog from './pt-br/confirmDialog_pt.json';
import ptProductVariations from './pt-br/productVariations_pt.json';
import ptStatementLayoutComponent from './pt-br/statementLayoutComponent_pt.json';
import ptCombinedProduct from './pt-br/combinedProduct_pt.json';
import ptMeasurementTable from './pt-br/measurementTable_pt.json';
import ptDashboard from './pt-br/dashboard_pt.json';
import ptMinimumOrder from './pt-br/minimumOrder_pt.json';
import ptFooter from './pt-br/footer_pt.json';
import ptSeoProductVariation from './pt-br/seoProductVariation_pt.json';
import ptThemeSelector from './pt-br/themeSelector_pt.json';
import ptThemes from './pt-br/themes_pt.json';
import ptQuestions from './pt-br/questions_pt.json';
import ptDomain from './pt-br/domain_pt-br.json';
import ptCoupons from './pt-br/coupons_pt.json';
import ptStoreBrandAssets from './pt-br/storeBrandAssets_pt.json';
import ptStorePhysicalData from './pt-br/storePhysicalData_pt.json';
import ptCommunity from './pt-br/community_pt.json';
import ptBlogPost from './pt-br/blogPost_pt.json';
import ptBlogCategory from './pt-br/blogCategory_pt.json';
import ptBlogDashboard from './pt-br/blogDashboard_pt.json';
import ptUserMenu from './pt-br/userMenu_pt.json';
import ptUserSettings from './pt-br/userSettings_pt.json';
import ptPaymentMethods from './pt-br/paymentMethods_pt.json';
import ptSignup from './pt-br/signup_pt.json';
import ptPasswordRecovery from './pt-br/passwordRecovery_pt.json';
import ptStores from './pt-br/stores_pt.json';
import ptPublishing from './pt-br/publishing_pt.json';
import ptProductGrid from './pt-br/productGrid_pt.json';
import ptScreenSearch from './pt-br/screenSearch_pt.json';
import ptStoreMode from './pt-br/storeMode_pt.json';
import ptUserProfile from './pt-br/userProfile_pt.json';

// Combine all translations
export const en = {
  ...enGeneral,
  errors: enErrors,
  contentEditor: enContentEditor,
  category: enCategory,
  login: enLogin,
  media: enMedia,
  product: enProduct,
  seo: enSeo,
  settings: enSettings,
  validation: enValidation,
  fileManager: enFileManager,
  originCep: enOriginCep,
  customerInfo: enCustomerInfo,
  addressList: enAddressList,
  notifications: enNotifications,
  emailNotifications: enEmailNotifications,
  seoConfig: enSeoConfig,
  couponManager: enCouponManager,
  couponForm: enCouponForm,
  storeSeo: enStoreSeo,
  urlRedirect: enUrlRedirect,
  socialMedia: enSocialMedia,
  socialLink: enSocialLink,
  domainUrl: enDomainUrl,
  editCategory: enEditCategory,
  promotions: enPromotions,
  pagination: enPagination,
  shippingConfig: enShippingConfig,
  toast: enToast,
  customer: enCustomer,
  layoutEditor: enLayoutEditor,
  collectionProduct: enCollectionProduct,
  confirmDialog: enConfirmDialog,
  productVariations: enProductVariations,
  statementLayoutComponent: enStatementLayoutComponent,
  combinedProduct: enCombinedProduct,
  measurementTable: enMeasurementTable,
  dashboard: enDashboard,
  minimumOrder: enMinimumOrder,
  footer: enFooter,
  seoProductVariation: enSeoProductVariation,
  themeSelector: enThemeSelector,
  themes: enThemes,
  questions: enQuestions,
  domain: enDomain,
  coupons: enCoupons,
  storeBrandAssets: enStoreBrandAssets,
  order: enOrder,
  pages: enPages,
  storePhysicalData: enStorePhysicalData,
  community: enCommunity,
  blogPost: enBlogPost,
  blogCategory: enBlogCategory,
  blogDashboard: enBlogDashboard,
  userMenu: enUserMenu,
  userSettings: enUserSettings,
  paymentMethods: enPaymentMethods,
  signup: enSignup,
  passwordRecovery: enPasswordRecovery,
  stores: enStores,
  screenSearch: enScreenSearch,
  storeMode: enStoreMode,
  userProfile: enUserProfile,
  publishing: enPublishing
};

export const pt = {
  ...ptGeneral,
  errors: ptErrors,
  contentEditor: ptContentEditor,
  category: ptCategory,
  login: ptLogin,
  media: ptMedia,
  product: ptProduct,
  seo: ptSeo,
  settings: ptSettings,
  validation: ptValidation,
  fileManager: ptFileManager,
  originCep: ptOriginCep,
  customerInfo: ptCustomerInfo,
  addressList: ptAddressList,
  notifications: ptNotifications,
  emailNotifications: ptEmailNotifications,
  seoConfig: ptSeoConfig,
  couponManager: ptCouponManager,
  couponForm: ptCouponForm,
  storeSeo: ptStoreSeo,
  urlRedirect: ptUrlRedirect,
  socialMedia: ptSocialMedia,
  socialLink: ptSocialLink,
  domainUrl: ptDomainUrl,
  editCategory: ptEditCategory,
  promotions: ptPromotions,
  pagination: ptPagination,
  shippingConfig: ptShippingConfig,
  toast: ptToast,
  customer: ptCustomer,
  layoutEditor: ptLayoutEditor,
  collectionProduct: ptCollectionProduct,
  confirmDialog: ptConfirmDialog,
  productVariations: ptProductVariations,
  statementLayoutComponent: ptStatementLayoutComponent,
  combinedProduct: ptCombinedProduct,
  measurementTable: ptMeasurementTable,
  dashboard: ptDashboard,
  minimumOrder: ptMinimumOrder,
  footer: ptFooter,
  seoProductVariation: ptSeoProductVariation,
  themeSelector: ptThemeSelector,
  themes: ptThemes,
  questions: ptQuestions,
  domain: ptDomain,
  coupons: ptCoupons,
  storeBrandAssets: ptStoreBrandAssets,
  order: ptOrder,
  pages: ptPages,
  storePhysicalData: ptStorePhysicalData,
  community: ptCommunity,
  blogPost: ptBlogPost,
  blogCategory: ptBlogCategory,
  blogDashboard: ptBlogDashboard,
  userMenu: ptUserMenu,
  userSettings: ptUserSettings,
  paymentMethods: ptPaymentMethods,
  signup: ptSignup,
  passwordRecovery: ptPasswordRecovery,
  stores: ptStores,
  publishing: ptPublishing,
  productGrid: ptProductGrid,
  screenSearch: ptScreenSearch,
  storeMode: ptStoreMode,
  userProfile: ptUserProfile
};

export default { en, pt };
